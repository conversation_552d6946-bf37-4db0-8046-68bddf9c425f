/*
 * Chava Electricals Website - Services Preview Component
 * Copyright (c) 2025 Eria Software Solutions and Services Pvt Ltd
 * All rights reserved.
 */

'use client';

import { motion } from 'framer-motion';
import Link from 'next/link';
import { useInView } from 'react-intersection-observer';
import { 
  Home, 
  Lightbulb, 
  Wrench, 
  Fan, 
  Zap, 
  Shield,
  ArrowRight 
} from 'lucide-react';

const ServicesPreview = () => {
  const [ref, inView] = useInView({
    triggerOnce: true,
    threshold: 0.1
  });

  const services = [
    {
      icon: <Home className="h-12 w-12" />,
      title: "घरगुती वायरिंग",
      description: "संपूर्ण घरासाठी सुरक्षित आणि आधुनिक वायरिंग सोल्यूशन",
      features: ["नवीन कनेक्शन", "पुनर्वायरिंग", "सुरक्षा तपासणी"]
    },
    {
      icon: <Lightbulb className="h-12 w-12" />,
      title: "सेन्सर लाइटिंग",
      description: "ऑटोमॅटिक सेन्सर लाइट इन्स्टॉलेशन आणि मेंटेनन्स",
      features: ["मोशन सेन्सर", "टाइमर लाइट्स", "स्मार्ट कंट्रोल"]
    },
    {
      icon: <Wrench className="h-12 w-12" />,
      title: "रिपेअर सर्विसेस",
      description: "सर्व प्रकारच्या इलेक्ट्रिकल समस्यांचे त्वरित निराकरण",
      features: ["इमर्जन्सी रिपेअर", "फॉल्ट डिटेक्शन", "प्रिव्हेंटिव्ह मेंटेनन्स"]
    },
    {
      icon: <Fan className="h-12 w-12" />,
      title: "फॅन इन्स्टॉलेशन",
      description: "सीलिंग फॅन, एक्झॉस्ट फॅन आणि इतर फॅन्सची स्थापना",
      features: ["सीलिंग फॅन", "एक्झॉस्ट फॅन", "टेबल फॅन रिपेअर"]
    },
    {
      icon: <Zap className="h-12 w-12" />,
      title: "स्विच बोर्ड",
      description: "आधुनिक स्विच बोर्ड इन्स्टॉलेशन आणि अपग्रेड",
      features: ["मॉड्यूलर स्विच", "डिमर कंट्रोल", "USB सॉकेट"]
    },
    {
      icon: <Shield className="h-12 w-12" />,
      title: "सेफ्टी चेकअप",
      description: "संपूर्ण इलेक्ट्रिकल सिस्टमची सुरक्षा तपासणी",
      features: ["अर्थिंग चेक", "लीकेज टेस्ट", "लोड बॅलन्सिंग"]
    }
  ];

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.2
      }
    }
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 50 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.6
      }
    }
  };

  return (
    <section className="py-20 bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <motion.div
          ref={ref}
          initial="hidden"
          animate={inView ? "visible" : "hidden"}
          variants={containerVariants}
          className="text-center mb-16"
        >
          <motion.h2
            variants={itemVariants}
            className="text-4xl md:text-5xl font-bold text-gray-900 mb-4 marathi-text"
          >
            आमच्या सेवा
          </motion.h2>
          <motion.h3
            variants={itemVariants}
            className="text-2xl md:text-3xl font-semibold text-orange-600 mb-6"
          >
            Our Services
          </motion.h3>
          <motion.p
            variants={itemVariants}
            className="text-xl text-gray-600 max-w-3xl mx-auto marathi-text"
          >
            आम्ही तुमच्या घरासाठी संपूर्ण इलेक्ट्रिकल सोल्यूशन्स प्रदान करतो
          </motion.p>
        </motion.div>

        <motion.div
          initial="hidden"
          animate={inView ? "visible" : "hidden"}
          variants={containerVariants}
          className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8"
        >
          {services.map((service, index) => (
            <motion.div
              key={index}
              variants={itemVariants}
              className="bg-white rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-2 p-8 group"
            >
              <div className="text-teal-600 mb-6 group-hover:scale-110 transition-transform duration-300">
                {service.icon}
              </div>
              
              <h4 className="text-xl font-bold text-gray-900 mb-4 marathi-text">
                {service.title}
              </h4>

              <p className="text-gray-600 mb-6 marathi-text">
                {service.description}
              </p>
              
              <ul className="space-y-2 mb-6">
                {service.features.map((feature, idx) => (
                  <li key={idx} className="flex items-center text-sm text-gray-600">
                    <div className="w-2 h-2 bg-teal-600 rounded-full mr-3"></div>
                    <span className="marathi-text">{feature}</span>
                  </li>
                ))}
              </ul>
              
              <Link
                href="/services"
                className="inline-flex items-center text-teal-600 font-semibold hover:text-teal-700 transition-colors duration-200"
              >
                <span className="marathi-text">अधिक माहिती</span>
                <ArrowRight className="h-4 w-4 ml-2 group-hover:translate-x-1 transition-transform duration-200" />
              </Link>
            </motion.div>
          ))}
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={inView ? { opacity: 1, y: 0 } : { opacity: 0, y: 30 }}
          transition={{ delay: 1.2, duration: 0.6 }}
          className="text-center mt-12"
        >
          <Link
            href="/services"
            className="inline-flex items-center bg-teal-600 text-white px-8 py-4 rounded-full font-bold text-lg hover:bg-teal-700 transition-all duration-300 transform hover:scale-105"
          >
            <span className="marathi-text">सर्व सेवा पहा</span>
            <ArrowRight className="h-6 w-6 ml-2" />
          </Link>
        </motion.div>
      </div>
    </section>
  );
};

export default ServicesPreview;
