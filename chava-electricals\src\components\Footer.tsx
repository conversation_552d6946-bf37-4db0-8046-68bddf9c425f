/*
 * Chava Electricals Website - Footer Component
 * Copyright (c) 2025 Eria Software Solutions and Services Pvt Ltd
 * All rights reserved.
 */

import Link from 'next/link';
import { Phone, Mail, MapPin, Zap, Clock, Shield } from 'lucide-react';

const Footer = () => {
  return (
    <footer className="bg-yellow-50 text-gray-800 border-t border-yellow-200">
      {/* Main Footer Content */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          {/* Company Info */}
          <div className="space-y-4">
            <div className="flex items-center space-x-2">
              <div className="bg-yellow-500 p-2 rounded-full">
                <Zap className="h-6 w-6 text-white" />
              </div>
              <div>
                <h3 className="text-xl font-bold marathi-text">छावा इलेक्ट्रिकल्स</h3>
              </div>
            </div>
            <p className="text-gray-600 text-sm">
              महाराष्ट्रातील विश्वसनीय इलेक्ट्रिकल सेवा प्रदाता. आम्ही गुणवत्तापूर्ण
              आणि सुरक्षित इलेक्ट्रिकल सोल्यूशन्स प्रदान करतो.
            </p>
            <div className="flex items-center space-x-2 text-sm text-gray-600">
              <Shield className="h-4 w-4 text-green-500" />
              <span>लायसन्स प्राप्त आणि विमाकृत</span>
            </div>
          </div>

          {/* Quick Links */}
          <div className="space-y-4">
            <h4 className="text-lg font-semibold text-yellow-600">द्रुत दुवे</h4>
            <ul className="space-y-2">
              <li>
                <Link href="/" className="text-gray-600 hover:text-yellow-600 transition-colors duration-200">
                  मुख्यपृष्ठ
                </Link>
              </li>
              <li>
                <Link href="/services" className="text-gray-600 hover:text-yellow-600 transition-colors duration-200">
                  आमच्या सेवा
                </Link>
              </li>
              <li>
                <Link href="/gallery" className="text-gray-600 hover:text-yellow-600 transition-colors duration-200">
                  आमचे काम
                </Link>
              </li>
              <li>
                <Link href="/about" className="text-gray-600 hover:text-yellow-600 transition-colors duration-200">
                  आमच्याबद्दल
                </Link>
              </li>
              <li>
                <Link href="/contact" className="text-gray-600 hover:text-yellow-600 transition-colors duration-200">
                  संपर्क
                </Link>
              </li>
            </ul>
          </div>

          {/* Services */}
          <div className="space-y-4">
            <h4 className="text-lg font-semibold text-yellow-600">आमच्या सेवा</h4>
            <ul className="space-y-2 text-sm text-gray-600">
              <li>• घरगुती वायरिंग</li>
              <li>• सेन्सर लाइटिंग</li>
              <li>• इलेक्ट्रिकल रिपेअरिंग</li>
              <li>• फॅन इन्स्टॉलेशन</li>
              <li>• स्विच बोर्ड सेटअप</li>
              <li>• इमर्जन्सी सर्विस</li>
            </ul>
          </div>

          {/* Contact Info */}
          <div className="space-y-4">
            <h4 className="text-lg font-semibold text-yellow-600">संपर्क माहिती</h4>
            <div className="space-y-3">
              <div className="flex items-center space-x-3">
                <Phone className="h-5 w-5 text-yellow-600" />
                <div>
                  <p className="text-sm text-gray-600">+91 98765 43210</p>
                  <p className="text-xs text-gray-500">24/7 उपलब्ध</p>
                </div>
              </div>
              <div className="flex items-center space-x-3">
                <Mail className="h-5 w-5 text-yellow-600" />
                <p className="text-sm text-gray-600"><EMAIL></p>
              </div>
              <div className="flex items-center space-x-3">
                <MapPin className="h-5 w-5 text-yellow-600" />
                <p className="text-sm text-gray-600">पुणे, महाराष्ट्र</p>
              </div>
              <div className="flex items-center space-x-3">
                <Clock className="h-5 w-5 text-yellow-600" />
                <div>
                  <p className="text-sm text-gray-600">सोम - शनि: 8:00 - 20:00</p>
                  <p className="text-xs text-gray-500">रविवार: इमर्जन्सी सेवा</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Bottom Bar */}
      <div className="border-t border-yellow-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
          <div className="flex flex-col md:flex-row justify-between items-center">
            <p className="text-sm text-gray-600">
              © 2025 छावा इलेक्ट्रिकल्स. सर्व हक्क राखीव.
            </p>
            <p className="text-xs text-gray-500 mt-2 md:mt-0">
              Developed by{' '}
              <span className="text-yellow-600">Eria Software Solutions and Services Pvt Ltd</span>
            </p>
          </div>
        </div>
      </div>
    </footer>
  );
};

export default Footer;
