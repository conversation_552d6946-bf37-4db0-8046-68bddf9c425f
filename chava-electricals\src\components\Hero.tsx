/*
 * Chava Electricals Website - Hero Component
 * Copyright (c) 2025 Eria Software Solutions and Services Pvt Ltd
 * All rights reserved.
 */

'use client';

import { motion } from 'framer-motion';
import Link from 'next/link';
import Image from 'next/image';
import { Zap, Phone, ArrowRight, Shield, Clock, Award } from 'lucide-react';

const Hero = () => {
  return (
    <section className="relative min-h-screen flex items-center justify-center overflow-hidden">
      {/* Background with electrical theme */}
      <div className="absolute inset-0 bg-gradient-to-br from-blue-700 via-teal-600 to-green-600">
        <div className="absolute inset-0 bg-black/20"></div>
        {/* Animated electrical patterns */}
        <div className="absolute inset-0 opacity-10">
          <div className="absolute top-1/4 left-1/4 w-32 h-32 border-2 border-white rounded-full spark-animation"></div>
          <div className="absolute top-3/4 right-1/4 w-24 h-24 border-2 border-white rounded-full spark-animation" style={{animationDelay: '1s'}}></div>
          <div className="absolute top-1/2 left-1/2 w-16 h-16 border-2 border-white rounded-full spark-animation" style={{animationDelay: '2s'}}></div>
        </div>
      </div>

      <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center text-white">
        <motion.div
          initial={{ opacity: 0, y: 50 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          className="space-y-8"
        >
          {/* Logo and Title */}
          <div className="space-y-4">
            <motion.div
              initial={{ scale: 0 }}
              animate={{ scale: 1 }}
              transition={{ delay: 0.2, type: "spring", stiffness: 200 }}
              className="flex justify-center"
            >
              <div className="bg-white p-4 rounded-full glow-animation">
                <Zap className="h-16 w-16 text-blue-700" />
              </div>
            </motion.div>
            
            <motion.h1
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.4 }}
              className="text-5xl md:text-7xl font-bold marathi-text"
            >
              छावा इलेक्ट्रिकल्स
            </motion.h1>
          </div>

          {/* Tagline */}
          <motion.p
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.6 }}
            className="text-xl md:text-2xl text-blue-100 max-w-3xl mx-auto marathi-text"
          >
            आपल्या घरासाठी विश्वसनीय आणि गुणवत्तापूर्ण इलेक्ट्रिकल सेवा
          </motion.p>

          {/* Features */}
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 1.2 }}
            className="flex flex-wrap justify-center gap-6 text-sm"
          >
            <div className="flex items-center space-x-2 bg-white/20 px-4 py-2 rounded-full">
              <Shield className="h-5 w-5" />
              <span>लायसन्स प्राप्त</span>
            </div>
            <div className="flex items-center space-x-2 bg-white/20 px-4 py-2 rounded-full">
              <Clock className="h-5 w-5" />
              <span>24/7 सेवा</span>
            </div>
            <div className="flex items-center space-x-2 bg-white/20 px-4 py-2 rounded-full">
              <Award className="h-5 w-5" />
              <span>10+ वर्षांचा अनुभव</span>
            </div>
          </motion.div>

          {/* CTA Buttons */}
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 1.4 }}
            className="flex flex-col sm:flex-row gap-4 justify-center items-center"
          >
            <Link
              href="tel:+919876543210"
              className="bg-white text-blue-700 px-8 py-4 rounded-full font-bold text-lg hover:bg-blue-50 transition-all duration-300 transform hover:scale-105 flex items-center space-x-2 shadow-lg"
            >
              <Phone className="h-6 w-6" />
              <span className="marathi-text">आत्ताच कॉल करा</span>
            </Link>

            <Link
              href="/gallery"
              className="border-2 border-white text-white px-8 py-4 rounded-full font-bold text-lg hover:bg-white hover:text-blue-700 transition-all duration-300 transform hover:scale-105 flex items-center space-x-2"
            >
              <span className="marathi-text">आमचे काम पहा</span>
              <ArrowRight className="h-6 w-6" />
            </Link>
          </motion.div>

          {/* Scroll indicator */}
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 2 }}
            className="absolute bottom-8 left-1/2 transform -translate-x-1/2"
          >
            <motion.div
              animate={{ y: [0, 10, 0] }}
              transition={{ duration: 2, repeat: Infinity }}
              className="w-6 h-10 border-2 border-white rounded-full flex justify-center"
            >
              <motion.div
                animate={{ y: [0, 12, 0] }}
                transition={{ duration: 2, repeat: Infinity }}
                className="w-1 h-3 bg-white rounded-full mt-2"
              ></motion.div>
            </motion.div>
          </motion.div>
        </motion.div>
      </div>
    </section>
  );
};

export default Hero;
