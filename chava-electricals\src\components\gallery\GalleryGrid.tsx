/*
 * Chava Electricals Website - Gallery Grid Component
 * Copyright (c) 2025 Eria Software Solutions and Services Pvt Ltd
 * All rights reserved.
 */

'use client';

import { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { useInView } from 'react-intersection-observer';
import GalleryItem from './GalleryItem';
import GalleryModal from './GalleryModal';

// Sample gallery data - In a real application, this would come from a CMS or API
const galleryData = [
  {
    id: 1,
    type: 'photo',
    category: 'home-wiring',
    title: 'संपूर्ण घर वायरिंग - पुणे',
    description: '3BHK घरासाठी संपूर्ण इलेक्ट्रिकल वायरिंग प्रकल्प',
    location: 'पुणे',
    date: '2024-12-15',
    image: '/api/placeholder/400/300',
    thumbnail: '/api/placeholder/400/300'
  },
  {
    id: 2,
    type: 'video',
    category: 'sensor-lighting',
    title: 'सेन्सर लाइट इन्स्टॉलेशन',
    description: 'ऑटोमॅटिक सेन्सर लाइट्स बसवणे',
    location: 'नाशिक',
    date: '2024-12-10',
    image: '/api/placeholder/400/300',
    videoUrl: '/api/placeholder/video',
    thumbnail: '/api/placeholder/400/300'
  },
  {
    id: 3,
    type: 'photo',
    category: 'switch-board',
    title: 'मॉड्यूलर स्विच बोर्ड',
    description: 'आधुनिक मॉड्यूलर स्विच बोर्ड इन्स्टॉलेशन',
    location: 'मुंबई',
    date: '2024-12-08',
    image: '/api/placeholder/400/300',
    thumbnail: '/api/placeholder/400/300'
  },
  {
    id: 4,
    type: 'photo',
    category: 'fan-installation',
    title: 'सीलिंग फॅन इन्स्टॉलेशन',
    description: 'हाय स्पीड सीलिंग फॅन बसवणे',
    location: 'कोल्हापूर',
    date: '2024-12-05',
    image: '/api/placeholder/400/300',
    thumbnail: '/api/placeholder/400/300'
  },
  {
    id: 5,
    type: 'video',
    category: 'repairs',
    title: 'इमर्जन्सी रिपेअर सर्विस',
    description: 'रात्रीच्या वेळी इमर्जन्सी रिपेअर',
    location: 'औरंगाबाद',
    date: '2024-12-03',
    image: '/api/placeholder/400/300',
    videoUrl: '/api/placeholder/video',
    thumbnail: '/api/placeholder/400/300'
  },
  {
    id: 6,
    type: 'photo',
    category: 'home-wiring',
    title: 'नवीन घर वायरिंग',
    description: 'नवीन बांधकामातील वायरिंग काम',
    location: 'सांगली',
    date: '2024-12-01',
    image: '/api/placeholder/400/300',
    thumbnail: '/api/placeholder/400/300'
  }
];

interface GalleryGridProps {
  activeFilter?: string;
  activeMediaType?: string;
}

const GalleryGrid = ({ activeFilter = 'all', activeMediaType = 'all' }: GalleryGridProps = {}) => {
  const [filteredData, setFilteredData] = useState(galleryData);
  const [selectedItem, setSelectedItem] = useState<any>(null);
  const [ref, inView] = useInView({
    triggerOnce: true,
    threshold: 0.1
  });

  useEffect(() => {
    let filtered = galleryData;

    // Filter by category
    if (activeFilter !== 'all') {
      filtered = filtered.filter(item => item.category === activeFilter);
    }

    // Filter by media type
    if (activeMediaType !== 'all') {
      if (activeMediaType === 'photos') {
        filtered = filtered.filter(item => item.type === 'photo');
      } else if (activeMediaType === 'videos') {
        filtered = filtered.filter(item => item.type === 'video');
      }
    }

    setFilteredData(filtered);
  }, [activeFilter, activeMediaType]);

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1
      }
    }
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 50 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.6
      }
    }
  };

  return (
    <>
      <motion.div
        ref={ref}
        initial="hidden"
        animate={inView ? "visible" : "hidden"}
        variants={containerVariants}
        className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8"
      >
        <AnimatePresence mode="wait">
          {filteredData.map((item) => (
            <motion.div
              key={item.id}
              variants={itemVariants}
              layout
              initial={{ opacity: 0, scale: 0.8 }}
              animate={{ opacity: 1, scale: 1 }}
              exit={{ opacity: 0, scale: 0.8 }}
              transition={{ duration: 0.3 }}
            >
              <GalleryItem
                item={item}
                onClick={() => setSelectedItem(item)}
              />
            </motion.div>
          ))}
        </AnimatePresence>
      </motion.div>

      {filteredData.length === 0 && (
        <motion.div
          initial={{ opacity: 0, y: 50 }}
          animate={{ opacity: 1, y: 0 }}
          className="text-center py-16"
        >
          <div className="text-gray-400 mb-4">
            <svg className="w-16 h-16 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.172 16.172a4 4 0 015.656 0M9 12h6m-6-4h6m2 5.291A7.962 7.962 0 0112 15c-2.34 0-4.47-.881-6.08-2.33" />
            </svg>
          </div>
          <h3 className="text-xl font-semibold text-gray-600 mb-2 marathi-text">
            या फिल्टरसाठी कोणतेही परिणाम सापडले नाहीत
          </h3>
         
        </motion.div>
      )}

      {/* Gallery Modal */}
      <GalleryModal
        item={selectedItem}
        isOpen={!!selectedItem}
        onClose={() => setSelectedItem(null)}
      />
    </>
  );
};

export default GalleryGrid;
