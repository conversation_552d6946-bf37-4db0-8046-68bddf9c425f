/*
 * Chava Electricals Website - Store Hero Component
 * Copyright (c) 2025 Eria Software Solutions and Services Pvt Ltd
 * All rights reserved.
 */

'use client';

import { motion } from 'framer-motion';
import { ShoppingCart, Truck, Shield, Headphones, Star } from 'lucide-react';

interface StoreHeroProps {
  cartItemsCount: number;
  onCartClick: () => void;
}

const StoreHero = ({ cartItemsCount, onCartClick }: StoreHeroProps) => {
  const features = [
    {
      icon: <Truck className="h-5 w-5" />,
      text: "मोफत डिलिव्हरी",
      
    },
    {
      icon: <Shield className="h-5 w-5" />,
      text: "गुणवत्ता हमी",
    },
    {
      icon: <Headphones className="h-5 w-5" />,
      text: "24/7 सपोर्ट",
    },
    {
      icon: <Star className="h-5 w-5" />,
      text: "ब्रँडेड उत्पादने",
    }
  ];

  return (
    <section className="relative bg-gradient-to-br from-orange-600 via-orange-500 to-yellow-500 py-16">
      {/* Background Pattern */}
      <div className="absolute inset-0 opacity-10">
        <div className="absolute top-1/4 left-1/4 w-32 h-32 border-2 border-white rounded-full spark-animation"></div>
        <div className="absolute top-3/4 right-1/4 w-24 h-24 border-2 border-white rounded-full spark-animation" style={{animationDelay: '1s'}}></div>
        <div className="absolute top-1/2 left-1/2 w-16 h-16 border-2 border-white rounded-full spark-animation" style={{animationDelay: '2s'}}></div>
      </div>

      <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between items-start mb-8">
          {/* Title Section */}
          <motion.div
            initial={{ opacity: 0, y: 50 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="text-white"
          >
            <motion.h1
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.2 }}
              className="text-4xl md:text-5xl font-bold marathi-text mb-2"
            >
              ऑनलाइन स्टोअर
            </motion.h1>
            
           

            <motion.p
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.6 }}
              className="text-lg text-orange-100 max-w-2xl marathi-text"
            >
              घरी बसून इलेक्ट्रिकल उत्पादने ऑर्डर करा
            </motion.p>
          </motion.div>

          {/* Cart Button */}
          <motion.button
            initial={{ scale: 0 }}
            animate={{ scale: 1 }}
            transition={{ delay: 0.8, type: "spring", stiffness: 200 }}
            onClick={onCartClick}
            className="relative bg-white/20 backdrop-blur-sm text-white p-4 rounded-full hover:bg-white/30 transition-all duration-300 transform hover:scale-110"
          >
            <ShoppingCart className="h-6 w-6" />
            {cartItemsCount > 0 && (
              <span className="absolute -top-2 -right-2 bg-red-500 text-white text-xs rounded-full h-6 w-6 flex items-center justify-center font-bold">
                {cartItemsCount}
              </span>
            )}
          </motion.button>
        </div>

        {/* Features */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 1.0 }}
          className="grid grid-cols-2 md:grid-cols-4 gap-4"
        >
          {features.map((feature, index) => (
            <motion.div
              key={index}
              initial={{ scale: 0 }}
              animate={{ scale: 1 }}
              transition={{ delay: 1.2 + index * 0.1, type: "spring", stiffness: 200 }}
              className="bg-white/20 backdrop-blur-sm rounded-xl p-4 text-center text-white"
            >
              <div className="flex justify-center mb-2">
                {feature.icon}
              </div>
              <div className="text-sm marathi-text font-semibold">
                {feature.text}
              </div>
              <div className="text-xs text-orange-100">
                {feature.english}
              </div>
            </motion.div>
          ))}
        </motion.div>

        {/* Search Bar */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 1.4 }}
          className="mt-8 max-w-2xl mx-auto"
        >
          <div className="relative">
            <input
              type="text"
              placeholder="उत्पादन शोधा..."
              className="w-full px-6 py-4 rounded-full text-gray-900 placeholder-gray-500 focus:outline-none focus:ring-4 focus:ring-white/30"
            />
            <button className="absolute right-2 top-2 bg-orange-600 text-white p-2 rounded-full hover:bg-orange-700 transition-colors duration-200">
              <svg className="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
              </svg>
            </button>
          </div>
        </motion.div>
      </div>
    </section>
  );
};

export default StoreHero;
