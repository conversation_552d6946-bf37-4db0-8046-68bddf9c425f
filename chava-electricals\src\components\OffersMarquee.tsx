/*
 * Chava Electricals Website - Offers Marquee Component
 * Copyright (c) 2025 Eria Software Solutions and Services Pvt Ltd
 * All rights reserved.
 */

'use client';

import { motion } from 'framer-motion';
import { Zap, Percent, Gift, Clock } from 'lucide-react';

const OffersMarquee = () => {
  const offers = [
    {
      icon: <Percent className="h-6 w-6" />,
      text: "नवीन कनेक्शनवर 20% सूट"
    },
    {
      icon: <Gift className="h-6 w-6" />,
      text: "मोफत होम इन्स्पेक्शन"
    },
    {
      icon: <Clock className="h-6 w-6" />,
      text: "24 तास इमर्जन्सी सर्विस"
    },
    {
      icon: <Zap className="h-6 w-6" />,
      text: "सेन्सर लाइट स्पेशल ऑफर"
    },
    {
      icon: <Percent className="h-6 w-6" />,
      text: "वायरिंग पॅकेजवर 15% सूट"
    }
  ];

  return (
    <section className="bg-gradient-to-r from-blue-700 via-teal-600 to-green-600 py-4 overflow-hidden">
      <div className="relative">
        <motion.div
          animate={{ x: [0, -100] }}
          transition={{
            duration: 30,
            repeat: Infinity,
            ease: "linear"
          }}
          className="flex space-x-12 whitespace-nowrap"
          style={{ width: 'calc(200% + 3rem)' }}
        >
          {/* First set of offers */}
          {offers.map((offer, index) => (
            <div
              key={`first-${index}`}
              className="flex items-center space-x-3 text-white font-semibold"
            >
              <div className="bg-white/20 p-2 rounded-full">
                {offer.icon}
              </div>
              <div className="text-center">
                <div className="text-lg marathi-text">{offer.text}</div>
              </div>
              <div className="text-2xl mx-8">•</div>
            </div>
          ))}
          
          {/* Duplicate set for seamless loop */}
          {offers.map((offer, index) => (
            <div
              key={`second-${index}`}
              className="flex items-center space-x-3 text-white font-semibold"
            >
              <div className="bg-white/20 p-2 rounded-full">
                {offer.icon}
              </div>
              <div className="text-center">
                <div className="text-lg marathi-text">{offer.text}</div>
              </div>
              <div className="text-2xl mx-8">•</div>
            </div>
          ))}
        </motion.div>
      </div>
    </section>
  );
};

export default OffersMarquee;
