{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Chava%20Electricals/chava-electricals/src/components/Navbar.tsx"], "sourcesContent": ["/*\n * Chava Electricals Website - Navigation Component\n * Copyright (c) 2025 Eria Software Solutions and Services Pvt Ltd\n * All rights reserved.\n */\n\n'use client';\n\nimport { useState } from 'react';\nimport Link from 'next/link';\nimport { Menu, X, Zap, Phone } from 'lucide-react';\nimport { motion, AnimatePresence } from 'framer-motion';\n\nconst Navbar = () => {\n  const [isOpen, setIsOpen] = useState(false);\n\n  const navItems = [\n    { href: '/', label: 'मुख्यपृष्ठ' },\n    { href: '/services', label: 'सेवा' },\n    { href: '/gallery', label: 'आमचे काम' },\n    { href: '/store', label: 'ऑनलाइन स्टोअर' },\n    { href: '/about', label: 'आमच्याबद्दल' },\n    { href: '/contact', label: 'संपर्क' },\n  ];\n\n  return (\n    <nav className=\"bg-gradient-to-r from-yellow-200 via-yellow-300 to-yellow-400 shadow-lg sticky top-0 z-50\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        <div className=\"flex justify-between items-center h-16\">\n          {/* Logo */}\n          <Link href=\"/\" className=\"flex items-center space-x-2\">\n            <div className=\"bg-white p-2 rounded-full shadow-md\">\n              <Zap className=\"h-6 w-6 text-yellow-600\" />\n            </div>\n            <div className=\"text-gray-800\">\n              <h1 className=\"text-xl font-bold marathi-text\">छावा इलेक्ट्रिकल्स</h1>\n            </div>\n          </Link>\n\n          {/* Desktop Navigation */}\n          <div className=\"hidden md:flex items-center space-x-8\">\n            {navItems.map((item) => (\n              <Link\n                key={item.href}\n                href={item.href}\n                className=\"text-gray-800 hover:text-yellow-700 transition-colors duration-200 font-medium marathi-text\"\n              >\n                {item.label}\n              </Link>\n            ))}\n            \n            {/* Call Button */}\n            <Link\n              href=\"tel:+919876543210\"\n              className=\"bg-yellow-500 text-white px-4 py-2 rounded-full font-semibold hover:bg-yellow-600 transition-colors duration-200 flex items-center space-x-2 shadow-md\"\n            >\n              <Phone className=\"h-4 w-4\" />\n              <span className=\"marathi-text\">कॉल करा</span>\n            </Link>\n          </div>\n\n          {/* Mobile menu button */}\n          <div className=\"md:hidden\">\n            <button\n              onClick={() => setIsOpen(!isOpen)}\n              className=\"text-white hover:text-blue-100 transition-colors duration-200\"\n            >\n              {isOpen ? <X className=\"h-6 w-6\" /> : <Menu className=\"h-6 w-6\" />}\n            </button>\n          </div>\n        </div>\n      </div>\n\n      {/* Mobile Navigation */}\n      <AnimatePresence>\n        {isOpen && (\n          <motion.div\n            initial={{ opacity: 0, height: 0 }}\n            animate={{ opacity: 1, height: 'auto' }}\n            exit={{ opacity: 0, height: 0 }}\n            className=\"md:hidden bg-yellow-300\"\n          >\n            <div className=\"px-2 pt-2 pb-3 space-y-1\">\n              {navItems.map((item) => (\n                <Link\n                  key={item.href}\n                  href={item.href}\n                  className=\"block px-3 py-2 text-gray-800 hover:bg-yellow-400 rounded-md transition-colors duration-200 marathi-text\"\n                  onClick={() => setIsOpen(false)}\n                >\n                  {item.label}\n                </Link>\n              ))}\n              <Link\n                href=\"tel:+919876543210\"\n                className=\"block px-3 py-2 bg-white text-blue-700 rounded-md font-semibold hover:bg-blue-50 transition-colors duration-200 flex items-center space-x-2\"\n                onClick={() => setIsOpen(false)}\n              >\n                <Phone className=\"h-4 w-4\" />\n                <span className=\"marathi-text\">कॉल करा</span>\n              </Link>\n            </div>\n          </motion.div>\n        )}\n      </AnimatePresence>\n    </nav>\n  );\n};\n\nexport default Navbar;\n"], "names": [], "mappings": "AAAA;;;;CAIC;;;;AAID;AACA;AACA;AAAA;AAAA;AAAA;AACA;AAAA;;;AALA;;;;;AAOA,MAAM,SAAS;;IACb,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAErC,MAAM,WAAW;QACf;YAAE,MAAM;YAAK,OAAO;QAAa;QACjC;YAAE,MAAM;YAAa,OAAO;QAAO;QACnC;YAAE,MAAM;YAAY,OAAO;QAAW;QACtC;YAAE,MAAM;YAAU,OAAO;QAAgB;QACzC;YAAE,MAAM;YAAU,OAAO;QAAc;QACvC;YAAE,MAAM;YAAY,OAAO;QAAS;KACrC;IAED,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC,+JAAA,CAAA,UAAI;4BAAC,MAAK;4BAAI,WAAU;;8CACvB,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC,mMAAA,CAAA,MAAG;wCAAC,WAAU;;;;;;;;;;;8CAEjB,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAG,WAAU;kDAAiC;;;;;;;;;;;;;;;;;sCAKnD,6LAAC;4BAAI,WAAU;;gCACZ,SAAS,GAAG,CAAC,CAAC,qBACb,6LAAC,+JAAA,CAAA,UAAI;wCAEH,MAAM,KAAK,IAAI;wCACf,WAAU;kDAET,KAAK,KAAK;uCAJN,KAAK,IAAI;;;;;8CASlB,6LAAC,+JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;;sDAEV,6LAAC,uMAAA,CAAA,QAAK;4CAAC,WAAU;;;;;;sDACjB,6LAAC;4CAAK,WAAU;sDAAe;;;;;;;;;;;;;;;;;;sCAKnC,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCACC,SAAS,IAAM,UAAU,CAAC;gCAC1B,WAAU;0CAET,uBAAS,6LAAC,+LAAA,CAAA,IAAC;oCAAC,WAAU;;;;;6FAAe,6LAAC,qMAAA,CAAA,OAAI;oCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAO9D,6LAAC,4LAAA,CAAA,kBAAe;0BACb,wBACC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,QAAQ;oBAAE;oBACjC,SAAS;wBAAE,SAAS;wBAAG,QAAQ;oBAAO;oBACtC,MAAM;wBAAE,SAAS;wBAAG,QAAQ;oBAAE;oBAC9B,WAAU;8BAEV,cAAA,6LAAC;wBAAI,WAAU;;4BACZ,SAAS,GAAG,CAAC,CAAC,qBACb,6LAAC,+JAAA,CAAA,UAAI;oCAEH,MAAM,KAAK,IAAI;oCACf,WAAU;oCACV,SAAS,IAAM,UAAU;8CAExB,KAAK,KAAK;mCALN,KAAK,IAAI;;;;;0CAQlB,6LAAC,+JAAA,CAAA,UAAI;gCACH,MAAK;gCACL,WAAU;gCACV,SAAS,IAAM,UAAU;;kDAEzB,6LAAC,uMAAA,CAAA,QAAK;wCAAC,WAAU;;;;;;kDACjB,6LAAC;wCAAK,WAAU;kDAAe;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQ/C;GA9FM;KAAA;uCAgGS", "debugId": null}}]}