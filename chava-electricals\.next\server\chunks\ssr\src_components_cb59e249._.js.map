{"version": 3, "sources": [], "sections": [{"offset": {"line": 5, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Chava%20Electricals/chava-electricals/src/components/Hero.tsx"], "sourcesContent": ["/*\n * Chava Electricals Website - Hero Component\n * Copyright (c) 2025 Eria Software Solutions and Services Pvt Ltd\n * All rights reserved.\n */\n\n'use client';\n\nimport { motion } from 'framer-motion';\nimport Link from 'next/link';\nimport Image from 'next/image';\nimport { Zap, Phone, ArrowRight, Shield, Clock, Award } from 'lucide-react';\n\nconst Hero = () => {\n  return (\n    <section className=\"relative min-h-screen flex items-center justify-center overflow-hidden\">\n      {/* Background with electrical theme */}\n      <div className=\"absolute inset-0 bg-gradient-to-br from-blue-700 via-teal-600 to-green-600\">\n        <div className=\"absolute inset-0 bg-black/20\"></div>\n        {/* Animated electrical patterns */}\n        <div className=\"absolute inset-0 opacity-10\">\n          <div className=\"absolute top-1/4 left-1/4 w-32 h-32 border-2 border-white rounded-full spark-animation\"></div>\n          <div className=\"absolute top-3/4 right-1/4 w-24 h-24 border-2 border-white rounded-full spark-animation\" style={{animationDelay: '1s'}}></div>\n          <div className=\"absolute top-1/2 left-1/2 w-16 h-16 border-2 border-white rounded-full spark-animation\" style={{animationDelay: '2s'}}></div>\n        </div>\n      </div>\n\n      <div className=\"relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center text-white\">\n        <motion.div\n          initial={{ opacity: 0, y: 50 }}\n          animate={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.8 }}\n          className=\"space-y-8\"\n        >\n          {/* Logo and Title */}\n          <div className=\"space-y-4\">\n            <motion.div\n              initial={{ scale: 0 }}\n              animate={{ scale: 1 }}\n              transition={{ delay: 0.2, type: \"spring\", stiffness: 200 }}\n              className=\"flex justify-center\"\n            >\n              <div className=\"bg-white p-4 rounded-full glow-animation\">\n                <Zap className=\"h-16 w-16 text-blue-700\" />\n              </div>\n            </motion.div>\n            \n            <motion.h1\n              initial={{ opacity: 0, y: 20 }}\n              animate={{ opacity: 1, y: 0 }}\n              transition={{ delay: 0.4 }}\n              className=\"text-5xl md:text-7xl font-bold marathi-text\"\n            >\n              छावा इलेक्ट्रिकल्स\n            </motion.h1>\n          </div>\n\n          {/* Tagline */}\n          <motion.p\n            initial={{ opacity: 0, y: 20 }}\n            animate={{ opacity: 1, y: 0 }}\n            transition={{ delay: 0.6 }}\n            className=\"text-xl md:text-2xl text-blue-100 max-w-3xl mx-auto marathi-text\"\n          >\n            आपल्या घरासाठी विश्वसनीय आणि गुणवत्तापूर्ण इलेक्ट्रिकल सेवा\n          </motion.p>\n\n          {/* Features */}\n          <motion.div\n            initial={{ opacity: 0, y: 30 }}\n            animate={{ opacity: 1, y: 0 }}\n            transition={{ delay: 1.2 }}\n            className=\"flex flex-wrap justify-center gap-6 text-sm\"\n          >\n            <div className=\"flex items-center space-x-2 bg-white/20 px-4 py-2 rounded-full\">\n              <Shield className=\"h-5 w-5\" />\n              <span>लायसन्स प्राप्त</span>\n            </div>\n            <div className=\"flex items-center space-x-2 bg-white/20 px-4 py-2 rounded-full\">\n              <Clock className=\"h-5 w-5\" />\n              <span>24/7 सेवा</span>\n            </div>\n            <div className=\"flex items-center space-x-2 bg-white/20 px-4 py-2 rounded-full\">\n              <Award className=\"h-5 w-5\" />\n              <span>10+ वर्षांचा अनुभव</span>\n            </div>\n          </motion.div>\n\n          {/* CTA Buttons */}\n          <motion.div\n            initial={{ opacity: 0, y: 30 }}\n            animate={{ opacity: 1, y: 0 }}\n            transition={{ delay: 1.4 }}\n            className=\"flex flex-col sm:flex-row gap-4 justify-center items-center\"\n          >\n            <Link\n              href=\"tel:+919876543210\"\n              className=\"bg-white text-blue-700 px-8 py-4 rounded-full font-bold text-lg hover:bg-blue-50 transition-all duration-300 transform hover:scale-105 flex items-center space-x-2 shadow-lg\"\n            >\n              <Phone className=\"h-6 w-6\" />\n              <span className=\"marathi-text\">आत्ताच कॉल करा</span>\n            </Link>\n\n            <Link\n              href=\"/gallery\"\n              className=\"border-2 border-white text-white px-8 py-4 rounded-full font-bold text-lg hover:bg-white hover:text-blue-700 transition-all duration-300 transform hover:scale-105 flex items-center space-x-2\"\n            >\n              <span className=\"marathi-text\">आमचे काम पहा</span>\n              <ArrowRight className=\"h-6 w-6\" />\n            </Link>\n          </motion.div>\n\n          {/* Scroll indicator */}\n          <motion.div\n            initial={{ opacity: 0 }}\n            animate={{ opacity: 1 }}\n            transition={{ delay: 2 }}\n            className=\"absolute bottom-8 left-1/2 transform -translate-x-1/2\"\n          >\n            <motion.div\n              animate={{ y: [0, 10, 0] }}\n              transition={{ duration: 2, repeat: Infinity }}\n              className=\"w-6 h-10 border-2 border-white rounded-full flex justify-center\"\n            >\n              <motion.div\n                animate={{ y: [0, 12, 0] }}\n                transition={{ duration: 2, repeat: Infinity }}\n                className=\"w-1 h-3 bg-white rounded-full mt-2\"\n              ></motion.div>\n            </motion.div>\n          </motion.div>\n        </motion.div>\n      </div>\n    </section>\n  );\n};\n\nexport default Hero;\n"], "names": [], "mappings": "AAAA;;;;CAIC;;;;AAID;AACA;AAEA;AAAA;AAAA;AAAA;AAAA;AAAA;AALA;;;;;AAOA,MAAM,OAAO;IACX,qBACE,8OAAC;QAAQ,WAAU;;0BAEjB,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;;;;;kCAEf,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;;;;;0CACf,8OAAC;gCAAI,WAAU;gCAA0F,OAAO;oCAAC,gBAAgB;gCAAI;;;;;;0CACrI,8OAAC;gCAAI,WAAU;gCAAyF,OAAO;oCAAC,gBAAgB;gCAAI;;;;;;;;;;;;;;;;;;0BAIxI,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAC5B,YAAY;wBAAE,UAAU;oBAAI;oBAC5B,WAAU;;sCAGV,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oCACT,SAAS;wCAAE,OAAO;oCAAE;oCACpB,SAAS;wCAAE,OAAO;oCAAE;oCACpB,YAAY;wCAAE,OAAO;wCAAK,MAAM;wCAAU,WAAW;oCAAI;oCACzD,WAAU;8CAEV,cAAA,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC,gMAAA,CAAA,MAAG;4CAAC,WAAU;;;;;;;;;;;;;;;;8CAInB,8OAAC,0LAAA,CAAA,SAAM,CAAC,EAAE;oCACR,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAG;oCAC7B,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAE;oCAC5B,YAAY;wCAAE,OAAO;oCAAI;oCACzB,WAAU;8CACX;;;;;;;;;;;;sCAMH,8OAAC,0LAAA,CAAA,SAAM,CAAC,CAAC;4BACP,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAC5B,YAAY;gCAAE,OAAO;4BAAI;4BACzB,WAAU;sCACX;;;;;;sCAKD,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAC5B,YAAY;gCAAE,OAAO;4BAAI;4BACzB,WAAU;;8CAEV,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,sMAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;sDAClB,8OAAC;sDAAK;;;;;;;;;;;;8CAER,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,oMAAA,CAAA,QAAK;4CAAC,WAAU;;;;;;sDACjB,8OAAC;sDAAK;;;;;;;;;;;;8CAER,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,oMAAA,CAAA,QAAK;4CAAC,WAAU;;;;;;sDACjB,8OAAC;sDAAK;;;;;;;;;;;;;;;;;;sCAKV,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAC5B,YAAY;gCAAE,OAAO;4BAAI;4BACzB,WAAU;;8CAEV,8OAAC,4JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;;sDAEV,8OAAC,oMAAA,CAAA,QAAK;4CAAC,WAAU;;;;;;sDACjB,8OAAC;4CAAK,WAAU;sDAAe;;;;;;;;;;;;8CAGjC,8OAAC,4JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;;sDAEV,8OAAC;4CAAK,WAAU;sDAAe;;;;;;sDAC/B,8OAAC,kNAAA,CAAA,aAAU;4CAAC,WAAU;;;;;;;;;;;;;;;;;;sCAK1B,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,SAAS;4BAAE;4BACtB,SAAS;gCAAE,SAAS;4BAAE;4BACtB,YAAY;gCAAE,OAAO;4BAAE;4BACvB,WAAU;sCAEV,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,SAAS;oCAAE,GAAG;wCAAC;wCAAG;wCAAI;qCAAE;gCAAC;gCACzB,YAAY;oCAAE,UAAU;oCAAG,QAAQ;gCAAS;gCAC5C,WAAU;0CAEV,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oCACT,SAAS;wCAAE,GAAG;4CAAC;4CAAG;4CAAI;yCAAE;oCAAC;oCACzB,YAAY;wCAAE,UAAU;wCAAG,QAAQ;oCAAS;oCAC5C,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQ1B;uCAEe", "debugId": null}}, {"offset": {"line": 411, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Chava%20Electricals/chava-electricals/src/components/OffersMarquee.tsx"], "sourcesContent": ["/*\n * Chava Electricals Website - Offers Marquee Component\n * Copyright (c) 2025 Eria Software Solutions and Services Pvt Ltd\n * All rights reserved.\n */\n\n'use client';\n\nimport { motion } from 'framer-motion';\nimport { Zap, Percent, Gift, Clock } from 'lucide-react';\n\nconst OffersMarquee = () => {\n  const offers = [\n    {\n      icon: <Percent className=\"h-6 w-6\" />,\n      text: \"नवीन कनेक्शनवर 20% सूट\"\n    },\n    {\n      icon: <Gift className=\"h-6 w-6\" />,\n      text: \"मोफत होम इन्स्पेक्शन\"\n    },\n    {\n      icon: <Clock className=\"h-6 w-6\" />,\n      text: \"24 तास इमर्जन्सी सर्विस\"\n    },\n    {\n      icon: <Zap className=\"h-6 w-6\" />,\n      text: \"सेन्सर लाइट स्पेशल ऑफर\"\n    },\n    {\n      icon: <Percent className=\"h-6 w-6\" />,\n      text: \"वायरिंग पॅकेजवर 15% सूट\"\n    }\n  ];\n\n  return (\n    <section className=\"bg-gradient-to-r from-blue-700 via-teal-600 to-green-600 py-4 overflow-hidden\">\n      <div className=\"relative\">\n        <motion.div\n          animate={{ x: [0, -100] }}\n          transition={{\n            duration: 30,\n            repeat: Infinity,\n            ease: \"linear\"\n          }}\n          className=\"flex space-x-12 whitespace-nowrap\"\n          style={{ width: 'calc(200% + 3rem)' }}\n        >\n          {/* First set of offers */}\n          {offers.map((offer, index) => (\n            <div\n              key={`first-${index}`}\n              className=\"flex items-center space-x-3 text-white font-semibold\"\n            >\n              <div className=\"bg-white/20 p-2 rounded-full\">\n                {offer.icon}\n              </div>\n              <div className=\"text-center\">\n                <div className=\"text-lg marathi-text\">{offer.text}</div>\n              </div>\n              <div className=\"text-2xl mx-8\">•</div>\n            </div>\n          ))}\n          \n          {/* Duplicate set for seamless loop */}\n          {offers.map((offer, index) => (\n            <div\n              key={`second-${index}`}\n              className=\"flex items-center space-x-3 text-white font-semibold\"\n            >\n              <div className=\"bg-white/20 p-2 rounded-full\">\n                {offer.icon}\n              </div>\n              <div className=\"text-center\">\n                <div className=\"text-lg marathi-text\">{offer.text}</div>\n              </div>\n              <div className=\"text-2xl mx-8\">•</div>\n            </div>\n          ))}\n        </motion.div>\n      </div>\n    </section>\n  );\n};\n\nexport default OffersMarquee;\n"], "names": [], "mappings": "AAAA;;;;CAIC;;;;AAID;AACA;AAAA;AAAA;AAAA;AAHA;;;;AAKA,MAAM,gBAAgB;IACpB,MAAM,SAAS;QACb;YACE,oBAAM,8OAAC,wMAAA,CAAA,UAAO;gBAAC,WAAU;;;;;;YACzB,MAAM;QACR;QACA;YACE,oBAAM,8OAAC,kMAAA,CAAA,OAAI;gBAAC,WAAU;;;;;;YACtB,MAAM;QACR;QACA;YACE,oBAAM,8OAAC,oMAAA,CAAA,QAAK;gBAAC,WAAU;;;;;;YACvB,MAAM;QACR;QACA;YACE,oBAAM,8OAAC,gMAAA,CAAA,MAAG;gBAAC,WAAU;;;;;;YACrB,MAAM;QACR;QACA;YACE,oBAAM,8OAAC,wMAAA,CAAA,UAAO;gBAAC,WAAU;;;;;;YACzB,MAAM;QACR;KACD;IAED,qBACE,8OAAC;QAAQ,WAAU;kBACjB,cAAA,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,SAAS;oBAAE,GAAG;wBAAC;wBAAG,CAAC;qBAAI;gBAAC;gBACxB,YAAY;oBACV,UAAU;oBACV,QAAQ;oBACR,MAAM;gBACR;gBACA,WAAU;gBACV,OAAO;oBAAE,OAAO;gBAAoB;;oBAGnC,OAAO,GAAG,CAAC,CAAC,OAAO,sBAClB,8OAAC;4BAEC,WAAU;;8CAEV,8OAAC;oCAAI,WAAU;8CACZ,MAAM,IAAI;;;;;;8CAEb,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;kDAAwB,MAAM,IAAI;;;;;;;;;;;8CAEnD,8OAAC;oCAAI,WAAU;8CAAgB;;;;;;;2BAT1B,CAAC,MAAM,EAAE,OAAO;;;;;oBAcxB,OAAO,GAAG,CAAC,CAAC,OAAO,sBAClB,8OAAC;4BAEC,WAAU;;8CAEV,8OAAC;oCAAI,WAAU;8CACZ,MAAM,IAAI;;;;;;8CAEb,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;kDAAwB,MAAM,IAAI;;;;;;;;;;;8CAEnD,8OAAC;oCAAI,WAAU;8CAAgB;;;;;;;2BAT1B,CAAC,OAAO,EAAE,OAAO;;;;;;;;;;;;;;;;;;;;;AAgBpC;uCAEe", "debugId": null}}, {"offset": {"line": 604, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Chava%20Electricals/chava-electricals/src/components/ServicesPreview.tsx"], "sourcesContent": ["/*\n * Chava Electricals Website - Services Preview Component\n * Copyright (c) 2025 Eria Software Solutions and Services Pvt Ltd\n * All rights reserved.\n */\n\n'use client';\n\nimport { motion } from 'framer-motion';\nimport Link from 'next/link';\nimport { useInView } from 'react-intersection-observer';\nimport { \n  Home, \n  Lightbulb, \n  Wrench, \n  Fan, \n  Zap, \n  Shield,\n  ArrowRight \n} from 'lucide-react';\n\nconst ServicesPreview = () => {\n  const [ref, inView] = useInView({\n    triggerOnce: true,\n    threshold: 0.1\n  });\n\n  const services = [\n    {\n      icon: <Home className=\"h-12 w-12\" />,\n      title: \"घरगुती वायरिंग\",\n      description: \"संपूर्ण घरासाठी सुरक्षित आणि आधुनिक वायरिंग सोल्यूशन\",\n      features: [\"नवीन कनेक्शन\", \"पुनर्वायरिंग\", \"सुरक्षा तपासणी\"]\n    },\n    {\n      icon: <Lightbulb className=\"h-12 w-12\" />,\n      title: \"सेन्सर लाइटिंग\",\n      description: \"ऑटोमॅटिक सेन्सर लाइट इन्स्टॉलेशन आणि मेंटेनन्स\",\n      features: [\"मोशन सेन्सर\", \"टाइमर लाइट्स\", \"स्मार्ट कंट्रोल\"]\n    },\n    {\n      icon: <Wrench className=\"h-12 w-12\" />,\n      title: \"रिपेअर सर्विसेस\",\n      description: \"सर्व प्रकारच्या इलेक्ट्रिकल समस्यांचे त्वरित निराकरण\",\n      features: [\"इमर्जन्सी रिपेअर\", \"फॉल्ट डिटेक्शन\", \"प्रिव्हेंटिव्ह मेंटेनन्स\"]\n    },\n    {\n      icon: <Fan className=\"h-12 w-12\" />,\n      title: \"फॅन इन्स्टॉलेशन\",\n      description: \"सीलिंग फॅन, एक्झॉस्ट फॅन आणि इतर फॅन्सची स्थापना\",\n      features: [\"सीलिंग फॅन\", \"एक्झॉस्ट फॅन\", \"टेबल फॅन रिपेअर\"]\n    },\n    {\n      icon: <Zap className=\"h-12 w-12\" />,\n      title: \"स्विच बोर्ड\",\n      description: \"आधुनिक स्विच बोर्ड इन्स्टॉलेशन आणि अपग्रेड\",\n      features: [\"मॉड्यूलर स्विच\", \"डिमर कंट्रोल\", \"USB सॉकेट\"]\n    },\n    {\n      icon: <Shield className=\"h-12 w-12\" />,\n      title: \"सेफ्टी चेकअप\",\n      description: \"संपूर्ण इलेक्ट्रिकल सिस्टमची सुरक्षा तपासणी\",\n      features: [\"अर्थिंग चेक\", \"लीकेज टेस्ट\", \"लोड बॅलन्सिंग\"]\n    }\n  ];\n\n  const containerVariants = {\n    hidden: { opacity: 0 },\n    visible: {\n      opacity: 1,\n      transition: {\n        staggerChildren: 0.2\n      }\n    }\n  };\n\n  const itemVariants = {\n    hidden: { opacity: 0, y: 50 },\n    visible: {\n      opacity: 1,\n      y: 0,\n      transition: {\n        duration: 0.6\n      }\n    }\n  };\n\n  return (\n    <section className=\"py-20 bg-gray-50\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        <motion.div\n          ref={ref}\n          initial=\"hidden\"\n          animate={inView ? \"visible\" : \"hidden\"}\n          variants={containerVariants}\n          className=\"text-center mb-16\"\n        >\n          <motion.h2\n            variants={itemVariants}\n            className=\"text-4xl md:text-5xl font-bold text-gray-900 mb-4 marathi-text\"\n          >\n            आमच्या सेवा\n          </motion.h2>\n          <motion.h3\n            variants={itemVariants}\n            className=\"text-2xl md:text-3xl font-semibold text-orange-600 mb-6\"\n          >\n            Our Services\n          </motion.h3>\n          <motion.p\n            variants={itemVariants}\n            className=\"text-xl text-gray-600 max-w-3xl mx-auto marathi-text\"\n          >\n            आम्ही तुमच्या घरासाठी संपूर्ण इलेक्ट्रिकल सोल्यूशन्स प्रदान करतो\n          </motion.p>\n        </motion.div>\n\n        <motion.div\n          initial=\"hidden\"\n          animate={inView ? \"visible\" : \"hidden\"}\n          variants={containerVariants}\n          className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8\"\n        >\n          {services.map((service, index) => (\n            <motion.div\n              key={index}\n              variants={itemVariants}\n              className=\"bg-white rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-2 p-8 group\"\n            >\n              <div className=\"text-teal-600 mb-6 group-hover:scale-110 transition-transform duration-300\">\n                {service.icon}\n              </div>\n              \n              <h4 className=\"text-xl font-bold text-gray-900 mb-4 marathi-text\">\n                {service.title}\n              </h4>\n\n              <p className=\"text-gray-600 mb-6 marathi-text\">\n                {service.description}\n              </p>\n              \n              <ul className=\"space-y-2 mb-6\">\n                {service.features.map((feature, idx) => (\n                  <li key={idx} className=\"flex items-center text-sm text-gray-600\">\n                    <div className=\"w-2 h-2 bg-teal-600 rounded-full mr-3\"></div>\n                    <span className=\"marathi-text\">{feature}</span>\n                  </li>\n                ))}\n              </ul>\n              \n              <Link\n                href=\"/services\"\n                className=\"inline-flex items-center text-teal-600 font-semibold hover:text-teal-700 transition-colors duration-200\"\n              >\n                <span className=\"marathi-text\">अधिक माहिती</span>\n                <ArrowRight className=\"h-4 w-4 ml-2 group-hover:translate-x-1 transition-transform duration-200\" />\n              </Link>\n            </motion.div>\n          ))}\n        </motion.div>\n\n        <motion.div\n          initial={{ opacity: 0, y: 30 }}\n          animate={inView ? { opacity: 1, y: 0 } : { opacity: 0, y: 30 }}\n          transition={{ delay: 1.2, duration: 0.6 }}\n          className=\"text-center mt-12\"\n        >\n          <Link\n            href=\"/services\"\n            className=\"inline-flex items-center bg-teal-600 text-white px-8 py-4 rounded-full font-bold text-lg hover:bg-teal-700 transition-all duration-300 transform hover:scale-105\"\n          >\n            <span className=\"marathi-text\">सर्व सेवा पहा</span>\n            <ArrowRight className=\"h-6 w-6 ml-2\" />\n          </Link>\n        </motion.div>\n      </div>\n    </section>\n  );\n};\n\nexport default ServicesPreview;\n"], "names": [], "mappings": "AAAA;;;;CAIC;;;;AAID;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AALA;;;;;;AAeA,MAAM,kBAAkB;IACtB,MAAM,CAAC,KAAK,OAAO,GAAG,CAAA,GAAA,mKAAA,CAAA,YAAS,AAAD,EAAE;QAC9B,aAAa;QACb,WAAW;IACb;IAEA,MAAM,WAAW;QACf;YACE,oBAAM,8OAAC,mMAAA,CAAA,OAAI;gBAAC,WAAU;;;;;;YACtB,OAAO;YACP,aAAa;YACb,UAAU;gBAAC;gBAAgB;gBAAgB;aAAiB;QAC9D;QACA;YACE,oBAAM,8OAAC,4MAAA,CAAA,YAAS;gBAAC,WAAU;;;;;;YAC3B,OAAO;YACP,aAAa;YACb,UAAU;gBAAC;gBAAe;gBAAgB;aAAkB;QAC9D;QACA;YACE,oBAAM,8OAAC,sMAAA,CAAA,SAAM;gBAAC,WAAU;;;;;;YACxB,OAAO;YACP,aAAa;YACb,UAAU;gBAAC;gBAAoB;gBAAkB;aAA2B;QAC9E;QACA;YACE,oBAAM,8OAAC,gMAAA,CAAA,MAAG;gBAAC,WAAU;;;;;;YACrB,OAAO;YACP,aAAa;YACb,UAAU;gBAAC;gBAAc;gBAAgB;aAAkB;QAC7D;QACA;YACE,oBAAM,8OAAC,gMAAA,CAAA,MAAG;gBAAC,WAAU;;;;;;YACrB,OAAO;YACP,aAAa;YACb,UAAU;gBAAC;gBAAkB;gBAAgB;aAAY;QAC3D;QACA;YACE,oBAAM,8OAAC,sMAAA,CAAA,SAAM;gBAAC,WAAU;;;;;;YACxB,OAAO;YACP,aAAa;YACb,UAAU;gBAAC;gBAAe;gBAAe;aAAgB;QAC3D;KACD;IAED,MAAM,oBAAoB;QACxB,QAAQ;YAAE,SAAS;QAAE;QACrB,SAAS;YACP,SAAS;YACT,YAAY;gBACV,iBAAiB;YACnB;QACF;IACF;IAEA,MAAM,eAAe;QACnB,QAAQ;YAAE,SAAS;YAAG,GAAG;QAAG;QAC5B,SAAS;YACP,SAAS;YACT,GAAG;YACH,YAAY;gBACV,UAAU;YACZ;QACF;IACF;IAEA,qBACE,8OAAC;QAAQ,WAAU;kBACjB,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,KAAK;oBACL,SAAQ;oBACR,SAAS,SAAS,YAAY;oBAC9B,UAAU;oBACV,WAAU;;sCAEV,8OAAC,0LAAA,CAAA,SAAM,CAAC,EAAE;4BACR,UAAU;4BACV,WAAU;sCACX;;;;;;sCAGD,8OAAC,0LAAA,CAAA,SAAM,CAAC,EAAE;4BACR,UAAU;4BACV,WAAU;sCACX;;;;;;sCAGD,8OAAC,0LAAA,CAAA,SAAM,CAAC,CAAC;4BACP,UAAU;4BACV,WAAU;sCACX;;;;;;;;;;;;8BAKH,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAQ;oBACR,SAAS,SAAS,YAAY;oBAC9B,UAAU;oBACV,WAAU;8BAET,SAAS,GAAG,CAAC,CAAC,SAAS,sBACtB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BAET,UAAU;4BACV,WAAU;;8CAEV,8OAAC;oCAAI,WAAU;8CACZ,QAAQ,IAAI;;;;;;8CAGf,8OAAC;oCAAG,WAAU;8CACX,QAAQ,KAAK;;;;;;8CAGhB,8OAAC;oCAAE,WAAU;8CACV,QAAQ,WAAW;;;;;;8CAGtB,8OAAC;oCAAG,WAAU;8CACX,QAAQ,QAAQ,CAAC,GAAG,CAAC,CAAC,SAAS,oBAC9B,8OAAC;4CAAa,WAAU;;8DACtB,8OAAC;oDAAI,WAAU;;;;;;8DACf,8OAAC;oDAAK,WAAU;8DAAgB;;;;;;;2CAFzB;;;;;;;;;;8CAOb,8OAAC,4JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;;sDAEV,8OAAC;4CAAK,WAAU;sDAAe;;;;;;sDAC/B,8OAAC,kNAAA,CAAA,aAAU;4CAAC,WAAU;;;;;;;;;;;;;2BA9BnB;;;;;;;;;;8BAoCX,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,SAAS,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAE,IAAI;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7D,YAAY;wBAAE,OAAO;wBAAK,UAAU;oBAAI;oBACxC,WAAU;8BAEV,cAAA,8OAAC,4JAAA,CAAA,UAAI;wBACH,MAAK;wBACL,WAAU;;0CAEV,8OAAC;gCAAK,WAAU;0CAAe;;;;;;0CAC/B,8OAAC,kNAAA,CAAA,aAAU;gCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMlC;uCAEe", "debugId": null}}, {"offset": {"line": 964, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Chava%20Electricals/chava-electricals/src/components/WhyChooseUs.tsx"], "sourcesContent": ["/*\n * Chava Electricals Website - Why Choose Us Component\n * Copyright (c) 2025 Eria Software Solutions and Services Pvt Ltd\n * All rights reserved.\n */\n\n'use client';\n\nimport { motion } from 'framer-motion';\nimport { useInView } from 'react-intersection-observer';\nimport { \n  Shield, \n  Clock, \n  Award, \n  Users, \n  ThumbsUp, \n  Zap,\n  CheckCircle,\n  Star\n} from 'lucide-react';\n\nconst WhyChooseUs = () => {\n  const [ref, inView] = useInView({\n    triggerOnce: true,\n    threshold: 0.1\n  });\n\n  const features = [\n    {\n      icon: <Shield className=\"h-8 w-8\" />,\n      title: \"लायसन्स प्राप्त\",\n      description: \"सरकारी मान्यताप्राप्त आणि विमाकृत सेवा\"\n    },\n    {\n      icon: <Clock className=\"h-8 w-8\" />,\n      title: \"24/7 उपलब्ध\",\n      description: \"कधीही इमर्जन्सी सेवेसाठी उपलब्ध\"\n    },\n    {\n      icon: <Award className=\"h-8 w-8\" />,\n      title: \"10+ वर्षांचा अनुभव\",\n      description: \"दशकभराचा व्यावसायिक अनुभव\"\n    },\n    {\n      icon: <Users className=\"h-8 w-8\" />,\n      title: \"तज्ञ टीम\",\n      description: \"कुशल आणि प्रशिक्षित तंत्रज्ञ\"\n    },\n    {\n      icon: <ThumbsUp className=\"h-8 w-8\" />,\n      title: \"गुणवत्ता हमी\",\n      description: \"सर्व कामांची गुणवत्ता हमी\"\n    },\n    {\n      icon: <Zap className=\"h-8 w-8\" />,\n      title: \"त्वरित सेवा\",\n      description: \"वेळेवर आणि कार्यक्षम सेवा\"\n    }\n  ];\n\n  const stats = [\n    {\n      number: \"500+\",\n      label: \"समाधान केलेले ग्राहक\",\n      english: \"Satisfied Customers\"\n    },\n    {\n      number: \"1000+\",\n      label: \"पूर्ण केलेले प्रकल्प\",\n      english: \"Completed Projects\"\n    },\n    {\n      number: \"10+\",\n      label: \"वर्षांचा अनुभव\",\n      english: \"Years Experience\"\n    },\n    {\n      number: \"24/7\",\n      label: \"सेवा उपलब्धता\",\n      english: \"Service Availability\"\n    }\n  ];\n\n  const containerVariants = {\n    hidden: { opacity: 0 },\n    visible: {\n      opacity: 1,\n      transition: {\n        staggerChildren: 0.2\n      }\n    }\n  };\n\n  const itemVariants = {\n    hidden: { opacity: 0, y: 50 },\n    visible: {\n      opacity: 1,\n      y: 0,\n      transition: {\n        duration: 0.6\n      }\n    }\n  };\n\n  return (\n    <section className=\"py-20 bg-white\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        <motion.div\n          ref={ref}\n          initial=\"hidden\"\n          animate={inView ? \"visible\" : \"hidden\"}\n          variants={containerVariants}\n          className=\"text-center mb-16\"\n        >\n          <motion.h2\n            variants={itemVariants}\n            className=\"text-4xl md:text-5xl font-bold text-gray-900 mb-4 marathi-text\"\n          >\n            आम्हाला का निवडावे?\n          </motion.h2>\n          <motion.h3\n            variants={itemVariants}\n            className=\"text-2xl md:text-3xl font-semibold text-orange-600 mb-6\"\n          >\n            Why Choose Us?\n          </motion.h3>\n          <motion.p\n            variants={itemVariants}\n            className=\"text-xl text-gray-600 max-w-3xl mx-auto marathi-text\"\n          >\n            आम्ही महाराष्ट्रातील सर्वोत्तम इलेक्ट्रिकल सेवा प्रदाता आहोत\n          </motion.p>\n        </motion.div>\n\n        {/* Features Grid */}\n        <motion.div\n          initial=\"hidden\"\n          animate={inView ? \"visible\" : \"hidden\"}\n          variants={containerVariants}\n          className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-16\"\n        >\n          {features.map((feature, index) => (\n            <motion.div\n              key={index}\n              variants={itemVariants}\n              className=\"text-center p-6 rounded-xl bg-gray-50 hover:bg-teal-50 transition-all duration-300 transform hover:-translate-y-2 group\"\n            >\n              <div className=\"text-teal-600 mb-4 flex justify-center group-hover:scale-110 transition-transform duration-300\">\n                {feature.icon}\n              </div>\n              <h4 className=\"text-lg font-bold text-gray-900 mb-3 marathi-text\">\n                {feature.title}\n              </h4>\n              <p className=\"text-gray-600 marathi-text\">\n                {feature.description}\n              </p>\n            </motion.div>\n          ))}\n        </motion.div>\n\n        {/* Stats Section */}\n        <motion.div\n          initial=\"hidden\"\n          animate={inView ? \"visible\" : \"hidden\"}\n          variants={containerVariants}\n          className=\"bg-gradient-to-r from-blue-700 to-teal-600 rounded-2xl p-8 md:p-12\"\n        >\n          <div className=\"grid grid-cols-2 md:grid-cols-4 gap-8 text-center text-white\">\n            {stats.map((stat, index) => (\n              <motion.div\n                key={index}\n                variants={itemVariants}\n                className=\"space-y-2\"\n              >\n                <motion.div\n                  initial={{ scale: 0 }}\n                  animate={inView ? { scale: 1 } : { scale: 0 }}\n                  transition={{ delay: index * 0.2, type: \"spring\", stiffness: 200 }}\n                  className=\"text-3xl md:text-4xl font-bold\"\n                >\n                  {stat.number}\n                </motion.div>\n                <div className=\"text-sm md:text-base marathi-text\">\n                  {stat.label}\n                </div>\n                <div className=\"text-xs text-orange-100\">\n                  {stat.english}\n                </div>\n              </motion.div>\n            ))}\n          </div>\n        </motion.div>\n\n        {/* Testimonial Preview */}\n        <motion.div\n          initial={{ opacity: 0, y: 50 }}\n          animate={inView ? { opacity: 1, y: 0 } : { opacity: 0, y: 50 }}\n          transition={{ delay: 1.0, duration: 0.6 }}\n          className=\"mt-16 text-center\"\n        >\n          <div className=\"bg-gray-50 rounded-2xl p-8 max-w-4xl mx-auto\">\n            <div className=\"flex justify-center mb-4\">\n              {[...Array(5)].map((_, i) => (\n                <Star key={i} className=\"h-6 w-6 text-yellow-400 fill-current\" />\n              ))}\n            </div>\n            <blockquote className=\"text-xl text-gray-700 mb-6 marathi-text\">\n              \"चावा इलेक्ट्रिकल्सची सेवा खूप चांगली आहे. त्यांनी आमच्या घराचे संपूर्ण वायरिंग वेळेवर आणि गुणवत्तेने केले.\"\n            </blockquote>\n            <cite className=\"text-gray-600 font-semibold\">\n              - राजेश पाटील, पुणे\n            </cite>\n          </div>\n        </motion.div>\n      </div>\n    </section>\n  );\n};\n\nexport default WhyChooseUs;\n"], "names": [], "mappings": "AAAA;;;;CAIC;;;;AAID;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAJA;;;;;AAeA,MAAM,cAAc;IAClB,MAAM,CAAC,KAAK,OAAO,GAAG,CAAA,GAAA,mKAAA,CAAA,YAAS,AAAD,EAAE;QAC9B,aAAa;QACb,WAAW;IACb;IAEA,MAAM,WAAW;QACf;YACE,oBAAM,8OAAC,sMAAA,CAAA,SAAM;gBAAC,WAAU;;;;;;YACxB,OAAO;YACP,aAAa;QACf;QACA;YACE,oBAAM,8OAAC,oMAAA,CAAA,QAAK;gBAAC,WAAU;;;;;;YACvB,OAAO;YACP,aAAa;QACf;QACA;YACE,oBAAM,8OAAC,oMAAA,CAAA,QAAK;gBAAC,WAAU;;;;;;YACvB,OAAO;YACP,aAAa;QACf;QACA;YACE,oBAAM,8OAAC,oMAAA,CAAA,QAAK;gBAAC,WAAU;;;;;;YACvB,OAAO;YACP,aAAa;QACf;QACA;YACE,oBAAM,8OAAC,8MAAA,CAAA,WAAQ;gBAAC,WAAU;;;;;;YAC1B,OAAO;YACP,aAAa;QACf;QACA;YACE,oBAAM,8OAAC,gMAAA,CAAA,MAAG;gBAAC,WAAU;;;;;;YACrB,OAAO;YACP,aAAa;QACf;KACD;IAED,MAAM,QAAQ;QACZ;YACE,QAAQ;YACR,OAAO;YACP,SAAS;QACX;QACA;YACE,QAAQ;YACR,OAAO;YACP,SAAS;QACX;QACA;YACE,QAAQ;YACR,OAAO;YACP,SAAS;QACX;QACA;YACE,QAAQ;YACR,OAAO;YACP,SAAS;QACX;KACD;IAED,MAAM,oBAAoB;QACxB,QAAQ;YAAE,SAAS;QAAE;QACrB,SAAS;YACP,SAAS;YACT,YAAY;gBACV,iBAAiB;YACnB;QACF;IACF;IAEA,MAAM,eAAe;QACnB,QAAQ;YAAE,SAAS;YAAG,GAAG;QAAG;QAC5B,SAAS;YACP,SAAS;YACT,GAAG;YACH,YAAY;gBACV,UAAU;YACZ;QACF;IACF;IAEA,qBACE,8OAAC;QAAQ,WAAU;kBACjB,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,KAAK;oBACL,SAAQ;oBACR,SAAS,SAAS,YAAY;oBAC9B,UAAU;oBACV,WAAU;;sCAEV,8OAAC,0LAAA,CAAA,SAAM,CAAC,EAAE;4BACR,UAAU;4BACV,WAAU;sCACX;;;;;;sCAGD,8OAAC,0LAAA,CAAA,SAAM,CAAC,EAAE;4BACR,UAAU;4BACV,WAAU;sCACX;;;;;;sCAGD,8OAAC,0LAAA,CAAA,SAAM,CAAC,CAAC;4BACP,UAAU;4BACV,WAAU;sCACX;;;;;;;;;;;;8BAMH,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAQ;oBACR,SAAS,SAAS,YAAY;oBAC9B,UAAU;oBACV,WAAU;8BAET,SAAS,GAAG,CAAC,CAAC,SAAS,sBACtB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BAET,UAAU;4BACV,WAAU;;8CAEV,8OAAC;oCAAI,WAAU;8CACZ,QAAQ,IAAI;;;;;;8CAEf,8OAAC;oCAAG,WAAU;8CACX,QAAQ,KAAK;;;;;;8CAEhB,8OAAC;oCAAE,WAAU;8CACV,QAAQ,WAAW;;;;;;;2BAXjB;;;;;;;;;;8BAkBX,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAQ;oBACR,SAAS,SAAS,YAAY;oBAC9B,UAAU;oBACV,WAAU;8BAEV,cAAA,8OAAC;wBAAI,WAAU;kCACZ,MAAM,GAAG,CAAC,CAAC,MAAM,sBAChB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gCAET,UAAU;gCACV,WAAU;;kDAEV,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wCACT,SAAS;4CAAE,OAAO;wCAAE;wCACpB,SAAS,SAAS;4CAAE,OAAO;wCAAE,IAAI;4CAAE,OAAO;wCAAE;wCAC5C,YAAY;4CAAE,OAAO,QAAQ;4CAAK,MAAM;4CAAU,WAAW;wCAAI;wCACjE,WAAU;kDAET,KAAK,MAAM;;;;;;kDAEd,8OAAC;wCAAI,WAAU;kDACZ,KAAK,KAAK;;;;;;kDAEb,8OAAC;wCAAI,WAAU;kDACZ,KAAK,OAAO;;;;;;;+BAhBV;;;;;;;;;;;;;;;8BAwBb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,SAAS,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAE,IAAI;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7D,YAAY;wBAAE,OAAO;wBAAK,UAAU;oBAAI;oBACxC,WAAU;8BAEV,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CACZ;uCAAI,MAAM;iCAAG,CAAC,GAAG,CAAC,CAAC,GAAG,kBACrB,8OAAC,kMAAA,CAAA,OAAI;wCAAS,WAAU;uCAAb;;;;;;;;;;0CAGf,8OAAC;gCAAW,WAAU;0CAA0C;;;;;;0CAGhE,8OAAC;gCAAK,WAAU;0CAA8B;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQ1D;uCAEe", "debugId": null}}, {"offset": {"line": 1338, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Chava%20Electricals/chava-electricals/src/components/TestimonialsSection.tsx"], "sourcesContent": ["/*\n * Chava Electricals Website - Testimonials Section Component\n * Copyright (c) 2025 Eria Software Solutions and Services Pvt Ltd\n * All rights reserved.\n */\n\n'use client';\n\nimport { motion } from 'framer-motion';\nimport { useInView } from 'react-intersection-observer';\nimport { Star, Quote } from 'lucide-react';\n\nconst TestimonialsSection = () => {\n  const [ref, inView] = useInView({\n    triggerOnce: true,\n    threshold: 0.1\n  });\n\n  const testimonials = [\n    {\n      name: \"सुनील शर्मा\",\n      location: \"पुणे\",\n      rating: 5,\n      text: \"छावा इलेक्ट्रिकल्सने आमच्या घराचे संपूर्ण वायरिंग अतिशय व्यावसायिकतेने केले. त्यांची सेवा उत्कृष्ट आहे.\",\n      service: \"घरगुती वायरिंग\"\n    },\n    {\n      name: \"प्रिया देशमुख\",\n      location: \"नाशिक\",\n      rating: 5,\n      text: \"सेन्सर लाइट्स लावण्यासाठी त्यांना बोलावले होते. काम अतिशय नीटनेटके आणि वेळेवर झाले.\",\n      service: \"सेन्सर लाइटिंग\"\n    },\n    {\n      name: \"अमित पाटील\",\n      location: \"मुंबई\",\n      rating: 5,\n      text: \"इमर्जन्सी मध्ये त्यांनी लगेच मदत केली. 24/7 सेवा खरोखरच उपलब्ध आहे.\",\n      service: \"इमर्जन्सी सर्विस\"\n    },\n    {\n      name: \"मीरा जोशी\",\n      location: \"कोल्हापूर\",\n      rating: 5,\n      text: \"फॅन इन्स्टॉलेशनचे काम उत्तम झाले. किंमत देखील योग्य होती.\",\n      service: \"फॅन इन्स्टॉलेशन\"\n    },\n    {\n      name: \"विकास कुलकर्णी\",\n      location: \"औरंगाबाद\",\n      rating: 5,\n      text: \"स्विच बोर्ड अपग्रेड करण्यासाठी त्यांची सेवा घेतली. अतिशय समाधानकारक अनुभव.\",\n      service: \"स्विच बोर्ड\"\n    },\n    {\n      name: \"संगीता राणे\",\n      location: \"सांगली\",\n      rating: 5,\n      text: \"सेफ्टी चेकअप केल्यानंतर घरातील सर्व इलेक्ट्रिकल समस्या सुटल्या.\",\n      service: \"सेफ्टी चेकअप\"\n    }\n  ];\n\n  const containerVariants = {\n    hidden: { opacity: 0 },\n    visible: {\n      opacity: 1,\n      transition: {\n        staggerChildren: 0.2\n      }\n    }\n  };\n\n  const itemVariants = {\n    hidden: { opacity: 0, y: 50 },\n    visible: {\n      opacity: 1,\n      y: 0,\n      transition: {\n        duration: 0.6\n      }\n    }\n  };\n\n  return (\n    <section className=\"py-20 bg-gray-50\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        <motion.div\n          ref={ref}\n          initial=\"hidden\"\n          animate={inView ? \"visible\" : \"hidden\"}\n          variants={containerVariants}\n          className=\"text-center mb-16\"\n        >\n          <motion.h2\n            variants={itemVariants}\n            className=\"text-4xl md:text-5xl font-bold text-gray-900 mb-4 marathi-text\"\n          >\n            ग्राहकांचे अनुभव\n          </motion.h2>\n          <motion.h3\n            variants={itemVariants}\n            className=\"text-2xl md:text-3xl font-semibold text-orange-600 mb-6\"\n          >\n            Customer Testimonials\n          </motion.h3>\n          <motion.p\n            variants={itemVariants}\n            className=\"text-xl text-gray-600 max-w-3xl mx-auto marathi-text\"\n          >\n            आमच्या समाधान झालेल्या ग्राहकांचे अनुभव वाचा\n          </motion.p>\n        </motion.div>\n\n        <motion.div\n          initial=\"hidden\"\n          animate={inView ? \"visible\" : \"hidden\"}\n          variants={containerVariants}\n          className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8\"\n        >\n          {testimonials.map((testimonial, index) => (\n            <motion.div\n              key={index}\n              variants={itemVariants}\n              className=\"bg-white rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-2 p-6 relative\"\n            >\n              {/* Quote Icon */}\n              <div className=\"absolute top-4 right-4 text-teal-200\">\n                <Quote className=\"h-8 w-8\" />\n              </div>\n\n              {/* Rating */}\n              <div className=\"flex items-center mb-4\">\n                {[...Array(testimonial.rating)].map((_, i) => (\n                  <Star key={i} className=\"h-5 w-5 text-yellow-400 fill-current\" />\n                ))}\n              </div>\n\n              {/* Service Badge */}\n              <div className=\"inline-block bg-teal-100 text-teal-600 px-3 py-1 rounded-full text-sm font-semibold mb-4 marathi-text\">\n                {testimonial.service}\n              </div>\n\n              {/* Testimonial Text */}\n              <blockquote className=\"text-gray-700 mb-6 marathi-text leading-relaxed\">\n                \"{testimonial.text}\"\n              </blockquote>\n\n              {/* Customer Info */}\n              <div className=\"border-t pt-4\">\n                <div className=\"font-semibold text-gray-900 marathi-text\">\n                  {testimonial.name}\n                </div>\n                <div className=\"text-sm text-gray-500\">\n                  {testimonial.location}\n                </div>\n              </div>\n            </motion.div>\n          ))}\n        </motion.div>\n\n        {/* Call to Action */}\n        <motion.div\n          initial={{ opacity: 0, y: 30 }}\n          animate={inView ? { opacity: 1, y: 0 } : { opacity: 0, y: 30 }}\n          transition={{ delay: 1.2, duration: 0.6 }}\n          className=\"text-center mt-16\"\n        >\n          <div className=\"bg-gradient-to-r from-orange-600 to-yellow-500 rounded-2xl p-8 text-white\">\n            <h3 className=\"text-2xl md:text-3xl font-bold mb-4 marathi-text\">\n              तुम्हीही आमच्या समाधान झालेल्या ग्राहकांमध्ये सामील व्हा!\n            </h3>\n            <p className=\"text-lg mb-6 text-orange-100\">\n              Join our satisfied customers today!\n            </p>\n            <div className=\"flex flex-col sm:flex-row gap-4 justify-center\">\n              <a\n                href=\"tel:+919876543210\"\n                className=\"bg-white text-orange-600 px-8 py-3 rounded-full font-bold hover:bg-orange-50 transition-colors duration-200\"\n              >\n                <span className=\"marathi-text\">आत्ताच कॉल करा</span>\n              </a>\n              <a\n                href=\"/contact\"\n                className=\"border-2 border-white text-white px-8 py-3 rounded-full font-bold hover:bg-white hover:text-orange-600 transition-all duration-200\"\n              >\n                <span className=\"marathi-text\">संपर्क करा</span>\n              </a>\n            </div>\n          </div>\n        </motion.div>\n      </div>\n    </section>\n  );\n};\n\nexport default TestimonialsSection;\n"], "names": [], "mappings": "AAAA;;;;CAIC;;;;AAID;AACA;AACA;AAAA;AAJA;;;;;AAMA,MAAM,sBAAsB;IAC1B,MAAM,CAAC,KAAK,OAAO,GAAG,CAAA,GAAA,mKAAA,CAAA,YAAS,AAAD,EAAE;QAC9B,aAAa;QACb,WAAW;IACb;IAEA,MAAM,eAAe;QACnB;YACE,MAAM;YACN,UAAU;YACV,QAAQ;YACR,MAAM;YACN,SAAS;QACX;QACA;YACE,MAAM;YACN,UAAU;YACV,QAAQ;YACR,MAAM;YACN,SAAS;QACX;QACA;YACE,MAAM;YACN,UAAU;YACV,QAAQ;YACR,MAAM;YACN,SAAS;QACX;QACA;YACE,MAAM;YACN,UAAU;YACV,QAAQ;YACR,MAAM;YACN,SAAS;QACX;QACA;YACE,MAAM;YACN,UAAU;YACV,QAAQ;YAC<PERSON>,MAAM;YAC<PERSON>,SAAS;QACX;QACA;YACE,MAAM;YACN,UAAU;YACV,QAAQ;YACR,MAAM;YACN,SAAS;QACX;KACD;IAED,MAAM,oBAAoB;QACxB,QAAQ;YAAE,SAAS;QAAE;QACrB,SAAS;YACP,SAAS;YACT,YAAY;gBACV,iBAAiB;YACnB;QACF;IACF;IAEA,MAAM,eAAe;QACnB,QAAQ;YAAE,SAAS;YAAG,GAAG;QAAG;QAC5B,SAAS;YACP,SAAS;YACT,GAAG;YACH,YAAY;gBACV,UAAU;YACZ;QACF;IACF;IAEA,qBACE,8OAAC;QAAQ,WAAU;kBACjB,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,KAAK;oBACL,SAAQ;oBACR,SAAS,SAAS,YAAY;oBAC9B,UAAU;oBACV,WAAU;;sCAEV,8OAAC,0LAAA,CAAA,SAAM,CAAC,EAAE;4BACR,UAAU;4BACV,WAAU;sCACX;;;;;;sCAGD,8OAAC,0LAAA,CAAA,SAAM,CAAC,EAAE;4BACR,UAAU;4BACV,WAAU;sCACX;;;;;;sCAGD,8OAAC,0LAAA,CAAA,SAAM,CAAC,CAAC;4BACP,UAAU;4BACV,WAAU;sCACX;;;;;;;;;;;;8BAKH,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAQ;oBACR,SAAS,SAAS,YAAY;oBAC9B,UAAU;oBACV,WAAU;8BAET,aAAa,GAAG,CAAC,CAAC,aAAa,sBAC9B,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BAET,UAAU;4BACV,WAAU;;8CAGV,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC,oMAAA,CAAA,QAAK;wCAAC,WAAU;;;;;;;;;;;8CAInB,8OAAC;oCAAI,WAAU;8CACZ;2CAAI,MAAM,YAAY,MAAM;qCAAE,CAAC,GAAG,CAAC,CAAC,GAAG,kBACtC,8OAAC,kMAAA,CAAA,OAAI;4CAAS,WAAU;2CAAb;;;;;;;;;;8CAKf,8OAAC;oCAAI,WAAU;8CACZ,YAAY,OAAO;;;;;;8CAItB,8OAAC;oCAAW,WAAU;;wCAAkD;wCACpE,YAAY,IAAI;wCAAC;;;;;;;8CAIrB,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACZ,YAAY,IAAI;;;;;;sDAEnB,8OAAC;4CAAI,WAAU;sDACZ,YAAY,QAAQ;;;;;;;;;;;;;2BAhCpB;;;;;;;;;;8BAwCX,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,SAAS,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAE,IAAI;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7D,YAAY;wBAAE,OAAO;wBAAK,UAAU;oBAAI;oBACxC,WAAU;8BAEV,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAAmD;;;;;;0CAGjE,8OAAC;gCAAE,WAAU;0CAA+B;;;;;;0CAG5C,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCACC,MAAK;wCACL,WAAU;kDAEV,cAAA,8OAAC;4CAAK,WAAU;sDAAe;;;;;;;;;;;kDAEjC,8OAAC;wCACC,MAAK;wCACL,WAAU;kDAEV,cAAA,8OAAC;4CAAK,WAAU;sDAAe;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQ/C;uCAEe", "debugId": null}}]}