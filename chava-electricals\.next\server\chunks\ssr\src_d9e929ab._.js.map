{"version": 3, "sources": [], "sections": [{"offset": {"line": 5, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Chava%20Electricals/chava-electricals/src/components/gallery/GalleryHero.tsx"], "sourcesContent": ["/*\n * Chava Electricals Website - Gallery Hero Component\n * Copyright (c) 2025 Eria Software Solutions and Services Pvt Ltd\n * All rights reserved.\n */\n\n'use client';\n\nimport { motion } from 'framer-motion';\nimport { Camera, Video, Award, Users } from 'lucide-react';\n\nconst GalleryHero = () => {\n  const stats = [\n    {\n      icon: <Camera className=\"h-6 w-6\" />,\n      number: \"500+\",\n      label: \"प्रकल्प फोटो\",\n    },\n    {\n      icon: <Video className=\"h-6 w-6\" />,\n      label: \"काम व्हिडिओ\",\n    },\n    {\n      icon: <Award className=\"h-6 w-6\" />,\n      number: \"100%\",\n      label: \"समाधान दर\",\n    },\n    {\n      icon: <Users className=\"h-6 w-6\" />,\n      number: \"500+\",\n      label: \"खुश ग्राहक\",\n    }\n  ];\n\n  return (\n    <section className=\"relative bg-gradient-to-br from-orange-600 via-orange-500 to-yellow-500 py-20\">\n      {/* Background Pattern */}\n      <div className=\"absolute inset-0 opacity-10\">\n        <div className=\"absolute top-1/4 left-1/4 w-32 h-32 border-2 border-white rounded-full spark-animation\"></div>\n        <div className=\"absolute top-3/4 right-1/4 w-24 h-24 border-2 border-white rounded-full spark-animation\" style={{animationDelay: '1s'}}></div>\n        <div className=\"absolute top-1/2 left-1/2 w-16 h-16 border-2 border-white rounded-full spark-animation\" style={{animationDelay: '2s'}}></div>\n      </div>\n\n      <div className=\"relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center text-white\">\n        <motion.div\n          initial={{ opacity: 0, y: 50 }}\n          animate={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.8 }}\n          className=\"space-y-8\"\n        >\n          {/* Title */}\n          <div className=\"space-y-4\">\n            <motion.h1\n              initial={{ opacity: 0, y: 20 }}\n              animate={{ opacity: 1, y: 0 }}\n              transition={{ delay: 0.2 }}\n              className=\"text-5xl md:text-6xl font-bold marathi-text\"\n            >\n              आमचे काम\n            </motion.h1>\n            \n           \n          </div>\n\n          {/* Description */}\n          <motion.div\n            initial={{ opacity: 0, y: 20 }}\n            animate={{ opacity: 1, y: 0 }}\n            transition={{ delay: 0.6 }}\n            className=\"space-y-4\"\n          >\n            <p className=\"text-xl md:text-2xl text-orange-100 max-w-4xl mx-auto marathi-text\">\n              आमच्या पूर्ण केलेल्या प्रकल्पांचे फोटो आणि व्हिडिओ पहा\n            </p>\n            \n          </motion.div>\n\n          {/* Stats */}\n          <motion.div\n            initial={{ opacity: 0, y: 30 }}\n            animate={{ opacity: 1, y: 0 }}\n            transition={{ delay: 0.8 }}\n            className=\"grid grid-cols-2 md:grid-cols-4 gap-6 max-w-4xl mx-auto\"\n          >\n            {stats.map((stat, index) => (\n              <motion.div\n                key={index}\n                initial={{ scale: 0 }}\n                animate={{ scale: 1 }}\n                transition={{ delay: 1 + index * 0.1, type: \"spring\", stiffness: 200 }}\n                className=\"bg-white/20 backdrop-blur-sm rounded-xl p-4 text-center\"\n              >\n                <div className=\"flex justify-center mb-2\">\n                  {stat.icon}\n                </div>\n                {stat.number && (\n                  <div className=\"text-2xl font-bold mb-1\">\n                    {stat.number}\n                  </div>\n                )}\n                <div className=\"text-sm marathi-text\">\n                  {stat.label}\n                </div>\n                <div className=\"text-xs text-orange-100\">\n                  {stat.english}\n                </div>\n              </motion.div>\n            ))}\n          </motion.div>\n\n          {/* Scroll Indicator */}\n          <motion.div\n            initial={{ opacity: 0 }}\n            animate={{ opacity: 1 }}\n            transition={{ delay: 1.5 }}\n            className=\"pt-8\"\n          >\n            <motion.div\n              animate={{ y: [0, 10, 0] }}\n              transition={{ duration: 2, repeat: Infinity }}\n              className=\"text-orange-200\"\n            >\n              <p className=\"text-sm marathi-text mb-2\">खाली स्क्रॉल करा</p>\n              <div className=\"w-6 h-10 border-2 border-white rounded-full mx-auto flex justify-center\">\n                <motion.div\n                  animate={{ y: [0, 12, 0] }}\n                  transition={{ duration: 2, repeat: Infinity }}\n                  className=\"w-1 h-3 bg-white rounded-full mt-2\"\n                ></motion.div>\n              </div>\n            </motion.div>\n          </motion.div>\n        </motion.div>\n      </div>\n    </section>\n  );\n};\n\nexport default GalleryHero;\n"], "names": [], "mappings": "AAAA;;;;CAIC;;;;AAID;AACA;AAAA;AAAA;AAAA;AAHA;;;;AAKA,MAAM,cAAc;IAClB,MAAM,QAAQ;QACZ;YACE,oBAAM,8OAAC,sMAAA,CAAA,SAAM;gBAAC,WAAU;;;;;;YACxB,QAAQ;YACR,OAAO;QACT;QACA;YACE,oBAAM,8OAAC,oMAAA,CAAA,QAAK;gBAAC,WAAU;;;;;;YACvB,OAAO;QACT;QACA;YACE,oBAAM,8OAAC,oMAAA,CAAA,QAAK;gBAAC,WAAU;;;;;;YACvB,QAAQ;YACR,OAAO;QACT;QACA;YACE,oBAAM,8OAAC,oMAAA,CAAA,QAAK;gBAAC,WAAU;;;;;;YACvB,QAAQ;YACR,OAAO;QACT;KACD;IAED,qBACE,8OAAC;QAAQ,WAAU;;0BAEjB,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;;;;;kCACf,8OAAC;wBAAI,WAAU;wBAA0F,OAAO;4BAAC,gBAAgB;wBAAI;;;;;;kCACrI,8OAAC;wBAAI,WAAU;wBAAyF,OAAO;4BAAC,gBAAgB;wBAAI;;;;;;;;;;;;0BAGtI,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAC5B,YAAY;wBAAE,UAAU;oBAAI;oBAC5B,WAAU;;sCAGV,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,EAAE;gCACR,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAG;gCAC7B,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAE;gCAC5B,YAAY;oCAAE,OAAO;gCAAI;gCACzB,WAAU;0CACX;;;;;;;;;;;sCAQH,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAC5B,YAAY;gCAAE,OAAO;4BAAI;4BACzB,WAAU;sCAEV,cAAA,8OAAC;gCAAE,WAAU;0CAAqE;;;;;;;;;;;sCAOpF,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAC5B,YAAY;gCAAE,OAAO;4BAAI;4BACzB,WAAU;sCAET,MAAM,GAAG,CAAC,CAAC,MAAM,sBAChB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oCAET,SAAS;wCAAE,OAAO;oCAAE;oCACpB,SAAS;wCAAE,OAAO;oCAAE;oCACpB,YAAY;wCAAE,OAAO,IAAI,QAAQ;wCAAK,MAAM;wCAAU,WAAW;oCAAI;oCACrE,WAAU;;sDAEV,8OAAC;4CAAI,WAAU;sDACZ,KAAK,IAAI;;;;;;wCAEX,KAAK,MAAM,kBACV,8OAAC;4CAAI,WAAU;sDACZ,KAAK,MAAM;;;;;;sDAGhB,8OAAC;4CAAI,WAAU;sDACZ,KAAK,KAAK;;;;;;sDAEb,8OAAC;4CAAI,WAAU;sDACZ,KAAK,OAAO;;;;;;;mCAlBV;;;;;;;;;;sCAyBX,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,SAAS;4BAAE;4BACtB,SAAS;gCAAE,SAAS;4BAAE;4BACtB,YAAY;gCAAE,OAAO;4BAAI;4BACzB,WAAU;sCAEV,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,SAAS;oCAAE,GAAG;wCAAC;wCAAG;wCAAI;qCAAE;gCAAC;gCACzB,YAAY;oCAAE,UAAU;oCAAG,QAAQ;gCAAS;gCAC5C,WAAU;;kDAEV,8OAAC;wCAAE,WAAU;kDAA4B;;;;;;kDACzC,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4CACT,SAAS;gDAAE,GAAG;oDAAC;oDAAG;oDAAI;iDAAE;4CAAC;4CACzB,YAAY;gDAAE,UAAU;gDAAG,QAAQ;4CAAS;4CAC5C,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAS5B;uCAEe", "debugId": null}}, {"offset": {"line": 337, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Chava%20Electricals/chava-electricals/src/components/gallery/GalleryFilter.tsx"], "sourcesContent": ["/*\n * Chava Electricals Website - Gallery Filter Component\n * Copyright (c) 2025 Eria Software Solutions and Services Pvt Ltd\n * All rights reserved.\n */\n\n'use client';\n\nimport { useState } from 'react';\nimport { motion } from 'framer-motion';\nimport { \n  Home, \n  Lightbulb, \n  Wrench, \n  Fan, \n  Zap, \n  Shield,\n  Grid3X3,\n  Camera,\n  Video\n} from 'lucide-react';\n\ninterface GalleryFilterProps {\n  onFilterChange?: (filter: string) => void;\n  onMediaTypeChange?: (type: string) => void;\n}\n\nconst GalleryFilter = ({ onFilterChange, onMediaTypeChange }: GalleryFilterProps) => {\n  const [activeFilter, setActiveFilter] = useState('all');\n  const [activeMediaType, setActiveMediaType] = useState('all');\n\n  const categories = [\n    {\n      id: 'all',\n      icon: <Grid3X3 className=\"h-5 w-5\" />,\n      label: 'सर्व काम',\n      count: 45\n    },\n    {\n      id: 'home-wiring',\n      icon: <Home className=\"h-5 w-5\" />,\n      label: 'घरगुती वायरिंग',\n      count: 15\n    },\n    {\n      id: 'sensor-lighting',\n      icon: <Lightbulb className=\"h-5 w-5\" />,\n      english: 'Sensor Lighting',\n      count: 12\n    },\n    {\n      id: 'repairs',\n      icon: <Wrench className=\"h-5 w-5\" />,\n      label: 'रिपेअर सर्विस',\n      count: 8\n    },\n    {\n      id: 'fan-installation',\n      icon: <Fan className=\"h-5 w-5\" />,\n      label: 'फॅन इन्स्टॉलेशन',\n      count: 6\n    },\n    {\n      id: 'switch-board',\n      icon: <Zap className=\"h-5 w-5\" />,\n      label: 'स्विच बोर्ड',\n      count: 4\n    }\n  ];\n\n  const mediaTypes = [\n    {\n      id: 'all',\n      icon: <Grid3X3 className=\"h-4 w-4\" />,\n      label: 'सर्व',\n    },\n    {\n      id: 'photos',\n      icon: <Camera className=\"h-4 w-4\" />,\n      label: 'फोटो',\n    },\n    {\n      id: 'videos',\n      icon: <Video className=\"h-4 w-4\" />,\n      label: 'व्हिडिओ',\n    }\n  ];\n\n  const handleFilterChange = (filterId: string) => {\n    setActiveFilter(filterId);\n    onFilterChange?.(filterId);\n  };\n\n  const handleMediaTypeChange = (typeId: string) => {\n    setActiveMediaType(typeId);\n    onMediaTypeChange?.(typeId);\n  };\n\n  return (\n    <div className=\"mb-12\">\n      {/* Category Filters */}\n      <div className=\"mb-8\">\n        <motion.h3\n          initial={{ opacity: 0, y: 20 }}\n          animate={{ opacity: 1, y: 0 }}\n          className=\"text-2xl font-bold text-gray-900 mb-6 text-center marathi-text\"\n        >\n          काम प्रकार निवडा\n        </motion.h3>\n        \n        <div className=\"flex flex-wrap justify-center gap-3\">\n          {categories.map((category, index) => (\n            <motion.button\n              key={category.id}\n              initial={{ opacity: 0, y: 20 }}\n              animate={{ opacity: 1, y: 0 }}\n              transition={{ delay: index * 0.1 }}\n              onClick={() => handleFilterChange(category.id)}\n              className={`flex items-center space-x-2 px-4 py-3 rounded-full font-semibold transition-all duration-300 transform hover:scale-105 ${\n                activeFilter === category.id\n                  ? 'bg-orange-600 text-white shadow-lg'\n                  : 'bg-white text-gray-700 hover:bg-orange-50 hover:text-orange-600 shadow-md'\n              }`}\n            >\n              <span className={activeFilter === category.id ? 'text-white' : 'text-orange-600'}>\n                {category.icon}\n              </span>\n              <div className=\"text-left\">\n                <div className=\"text-sm marathi-text\">{category.label}</div>\n                <div className=\"text-xs opacity-75\">{category.english}</div>\n              </div>\n              <span className={`text-xs px-2 py-1 rounded-full ${\n                activeFilter === category.id\n                  ? 'bg-white/20 text-white'\n                  : 'bg-orange-100 text-orange-600'\n              }`}>\n                {category.count}\n              </span>\n            </motion.button>\n          ))}\n        </div>\n      </div>\n\n      {/* Media Type Filters */}\n      <div className=\"flex justify-center\">\n        <div className=\"bg-white rounded-full p-2 shadow-lg\">\n          <div className=\"flex space-x-2\">\n            {mediaTypes.map((type, index) => (\n              <motion.button\n                key={type.id}\n                initial={{ opacity: 0, scale: 0 }}\n                animate={{ opacity: 1, scale: 1 }}\n                transition={{ delay: 0.5 + index * 0.1 }}\n                onClick={() => handleMediaTypeChange(type.id)}\n                className={`flex items-center space-x-2 px-4 py-2 rounded-full font-medium transition-all duration-300 ${\n                  activeMediaType === type.id\n                    ? 'bg-orange-600 text-white'\n                    : 'text-gray-600 hover:bg-gray-100'\n                }`}\n              >\n                {type.icon}\n                <div className=\"text-center\">\n                  <div className=\"text-sm marathi-text\">{type.label}</div>\n                </div>\n              </motion.button>\n            ))}\n          </div>\n        </div>\n      </div>\n\n      {/* Active Filters Display */}\n      {(activeFilter !== 'all' || activeMediaType !== 'all') && (\n        <motion.div\n          initial={{ opacity: 0, y: 20 }}\n          animate={{ opacity: 1, y: 0 }}\n          className=\"mt-6 text-center\"\n        >\n          <div className=\"inline-flex items-center space-x-2 bg-orange-100 text-orange-800 px-4 py-2 rounded-full\">\n            <span className=\"text-sm marathi-text\">सक्रिय फिल्टर:</span>\n            {activeFilter !== 'all' && (\n              <span className=\"bg-orange-200 px-2 py-1 rounded text-xs marathi-text\">\n                {categories.find(c => c.id === activeFilter)?.label}\n              </span>\n            )}\n            {activeMediaType !== 'all' && (\n              <span className=\"bg-orange-200 px-2 py-1 rounded text-xs marathi-text\">\n                {mediaTypes.find(t => t.id === activeMediaType)?.label}\n              </span>\n            )}\n            <button\n              onClick={() => {\n                setActiveFilter('all');\n                setActiveMediaType('all');\n                onFilterChange?.('all');\n                onMediaTypeChange?.('all');\n              }}\n              className=\"text-orange-600 hover:text-orange-800 text-xs underline\"\n            >\n              साफ करा\n            </button>\n          </div>\n        </motion.div>\n      )}\n    </div>\n  );\n};\n\nexport default GalleryFilter;\n"], "names": [], "mappings": "AAAA;;;;CAIC;;;;AAID;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAJA;;;;;AAqBA,MAAM,gBAAgB,CAAC,EAAE,cAAc,EAAE,iBAAiB,EAAsB;IAC9E,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEvD,MAAM,aAAa;QACjB;YACE,IAAI;YACJ,oBAAM,8OAAC,4MAAA,CAAA,UAAO;gBAAC,WAAU;;;;;;YACzB,OAAO;YACP,OAAO;QACT;QACA;YACE,IAAI;YACJ,oBAAM,8OAAC,mMAAA,CAAA,OAAI;gBAAC,WAAU;;;;;;YACtB,OAAO;YACP,OAAO;QACT;QACA;YACE,IAAI;YACJ,oBAAM,8OAAC,4MAAA,CAAA,YAAS;gBAAC,WAAU;;;;;;YAC3B,SAAS;YACT,OAAO;QACT;QACA;YACE,IAAI;YACJ,oBAAM,8OAAC,sMAAA,CAAA,SAAM;gBAAC,WAAU;;;;;;YACxB,OAAO;YACP,OAAO;QACT;QACA;YACE,IAAI;YACJ,oBAAM,8OAAC,gMAAA,CAAA,MAAG;gBAAC,WAAU;;;;;;YACrB,OAAO;YACP,OAAO;QACT;QACA;YACE,IAAI;YACJ,oBAAM,8OAAC,gMAAA,CAAA,MAAG;gBAAC,WAAU;;;;;;YACrB,OAAO;YACP,OAAO;QACT;KACD;IAED,MAAM,aAAa;QACjB;YACE,IAAI;YACJ,oBAAM,8OAAC,4MAAA,CAAA,UAAO;gBAAC,WAAU;;;;;;YACzB,OAAO;QACT;QACA;YACE,IAAI;YACJ,oBAAM,8OAAC,sMAAA,CAAA,SAAM;gBAAC,WAAU;;;;;;YACxB,OAAO;QACT;QACA;YACE,IAAI;YACJ,oBAAM,8OAAC,oMAAA,CAAA,QAAK;gBAAC,WAAU;;;;;;YACvB,OAAO;QACT;KACD;IAED,MAAM,qBAAqB,CAAC;QAC1B,gBAAgB;QAChB,iBAAiB;IACnB;IAEA,MAAM,wBAAwB,CAAC;QAC7B,mBAAmB;QACnB,oBAAoB;IACtB;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,0LAAA,CAAA,SAAM,CAAC,EAAE;wBACR,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC7B,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAC5B,WAAU;kCACX;;;;;;kCAID,8OAAC;wBAAI,WAAU;kCACZ,WAAW,GAAG,CAAC,CAAC,UAAU,sBACzB,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;gCAEZ,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAG;gCAC7B,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAE;gCAC5B,YAAY;oCAAE,OAAO,QAAQ;gCAAI;gCACjC,SAAS,IAAM,mBAAmB,SAAS,EAAE;gCAC7C,WAAW,CAAC,uHAAuH,EACjI,iBAAiB,SAAS,EAAE,GACxB,uCACA,6EACJ;;kDAEF,8OAAC;wCAAK,WAAW,iBAAiB,SAAS,EAAE,GAAG,eAAe;kDAC5D,SAAS,IAAI;;;;;;kDAEhB,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DAAwB,SAAS,KAAK;;;;;;0DACrD,8OAAC;gDAAI,WAAU;0DAAsB,SAAS,OAAO;;;;;;;;;;;;kDAEvD,8OAAC;wCAAK,WAAW,CAAC,+BAA+B,EAC/C,iBAAiB,SAAS,EAAE,GACxB,2BACA,iCACJ;kDACC,SAAS,KAAK;;;;;;;+BAvBZ,SAAS,EAAE;;;;;;;;;;;;;;;;0BA+BxB,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;kCACZ,WAAW,GAAG,CAAC,CAAC,MAAM,sBACrB,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;gCAEZ,SAAS;oCAAE,SAAS;oCAAG,OAAO;gCAAE;gCAChC,SAAS;oCAAE,SAAS;oCAAG,OAAO;gCAAE;gCAChC,YAAY;oCAAE,OAAO,MAAM,QAAQ;gCAAI;gCACvC,SAAS,IAAM,sBAAsB,KAAK,EAAE;gCAC5C,WAAW,CAAC,2FAA2F,EACrG,oBAAoB,KAAK,EAAE,GACvB,6BACA,mCACJ;;oCAED,KAAK,IAAI;kDACV,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAI,WAAU;sDAAwB,KAAK,KAAK;;;;;;;;;;;;+BAb9C,KAAK,EAAE;;;;;;;;;;;;;;;;;;;;YAsBrB,CAAC,iBAAiB,SAAS,oBAAoB,KAAK,mBACnD,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,SAAS;oBAAE,SAAS;oBAAG,GAAG;gBAAG;gBAC7B,SAAS;oBAAE,SAAS;oBAAG,GAAG;gBAAE;gBAC5B,WAAU;0BAEV,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAK,WAAU;sCAAuB;;;;;;wBACtC,iBAAiB,uBAChB,8OAAC;4BAAK,WAAU;sCACb,WAAW,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,eAAe;;;;;;wBAGjD,oBAAoB,uBACnB,8OAAC;4BAAK,WAAU;sCACb,WAAW,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,kBAAkB;;;;;;sCAGrD,8OAAC;4BACC,SAAS;gCACP,gBAAgB;gCAChB,mBAAmB;gCACnB,iBAAiB;gCACjB,oBAAoB;4BACtB;4BACA,WAAU;sCACX;;;;;;;;;;;;;;;;;;;;;;;AAQb;uCAEe", "debugId": null}}, {"offset": {"line": 709, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Chava%20Electricals/chava-electricals/src/components/gallery/GalleryItem.tsx"], "sourcesContent": ["/*\n * Chava Electricals Website - Gallery Item Component\n * Copyright (c) 2025 Eria Software Solutions and Services Pvt Ltd\n * All rights reserved.\n */\n\n'use client';\n\nimport { motion } from 'framer-motion';\nimport { Play, Camera, MapPin, Calendar, Eye } from 'lucide-react';\nimport Image from 'next/image';\n\ninterface GalleryItemProps {\n  item: {\n    id: number;\n    type: 'photo' | 'video';\n    category: string;\n    title: string;\n    englishTitle: string;\n    description: string;\n    englishDescription: string;\n    location: string;\n    date: string;\n    image: string;\n    thumbnail: string;\n    videoUrl?: string;\n  };\n  onClick: () => void;\n}\n\nconst GalleryItem = ({ item, onClick }: GalleryItemProps) => {\n  const getCategoryBadge = (category: string) => {\n    const badges = {\n      'home-wiring': { label: 'घरगुती वायरिंग', color: 'bg-blue-500' },\n      'sensor-lighting': { label: 'सेन्सर लाइटिंग', color: 'bg-yellow-500' },\n      'repairs': { label: 'रिपेअर सर्विस', color: 'bg-red-500' },\n      'fan-installation': { label: 'फॅन इन्स्टॉलेशन', color: 'bg-green-500' },\n      'switch-board': { label: 'स्विच बोर्ड', color: 'bg-purple-500' },\n    };\n    return badges[category] || { label: category, color: 'bg-gray-500' };\n  };\n\n  const categoryBadge = getCategoryBadge(item.category);\n\n  return (\n    <motion.div\n      whileHover={{ y: -8 }}\n      whileTap={{ scale: 0.98 }}\n      className=\"bg-white rounded-xl shadow-lg hover:shadow-2xl transition-all duration-300 overflow-hidden cursor-pointer group\"\n      onClick={onClick}\n    >\n      {/* Image Container */}\n      <div className=\"relative aspect-[4/3] overflow-hidden\">\n        <Image\n          src={item.thumbnail}\n          alt={item.title}\n          fill\n          className=\"object-cover transition-transform duration-300 group-hover:scale-110\"\n        />\n        \n        {/* Overlay */}\n        <div className=\"absolute inset-0 bg-black/0 group-hover:bg-black/30 transition-all duration-300 flex items-center justify-center\">\n          <motion.div\n            initial={{ scale: 0, opacity: 0 }}\n            whileHover={{ scale: 1, opacity: 1 }}\n            className=\"bg-white/90 backdrop-blur-sm rounded-full p-4\"\n          >\n            {item.type === 'video' ? (\n              <Play className=\"h-8 w-8 text-orange-600\" />\n            ) : (\n              <Eye className=\"h-8 w-8 text-orange-600\" />\n            )}\n          </motion.div>\n        </div>\n\n        {/* Type Badge */}\n        <div className=\"absolute top-4 left-4\">\n          <div className={`flex items-center space-x-1 px-3 py-1 rounded-full text-white text-xs font-semibold ${\n            item.type === 'video' ? 'bg-red-500' : 'bg-blue-500'\n          }`}>\n            {item.type === 'video' ? (\n              <Play className=\"h-3 w-3\" />\n            ) : (\n              <Camera className=\"h-3 w-3\" />\n            )}\n            <span>{item.type === 'video' ? 'व्हिडिओ' : 'फोटो'}</span>\n          </div>\n        </div>\n\n        {/* Category Badge */}\n        <div className=\"absolute top-4 right-4\">\n          <div className={`px-3 py-1 rounded-full text-white text-xs font-semibold ${categoryBadge.color}`}>\n            {categoryBadge.label}\n          </div>\n        </div>\n      </div>\n\n      {/* Content */}\n      <div className=\"p-6\">\n        {/* Title */}\n        <h3 className=\"text-lg font-bold text-gray-900 mb-2 marathi-text group-hover:text-orange-600 transition-colors duration-200\">\n          {item.title}\n        </h3>\n        <h4 className=\"text-md font-semibold text-orange-600 mb-3\">\n          {item.englishTitle}\n        </h4>\n\n        {/* Description */}\n        <p className=\"text-gray-600 text-sm mb-4 marathi-text line-clamp-2\">\n          {item.description}\n        </p>\n        <p className=\"text-gray-500 text-xs mb-4 line-clamp-2\">\n          {item.englishDescription}\n        </p>\n\n        {/* Meta Information */}\n        <div className=\"flex items-center justify-between text-xs text-gray-500\">\n          <div className=\"flex items-center space-x-1\">\n            <MapPin className=\"h-3 w-3\" />\n            <span className=\"marathi-text\">{item.location}</span>\n          </div>\n          <div className=\"flex items-center space-x-1\">\n            <Calendar className=\"h-3 w-3\" />\n            <span>{new Date(item.date).toLocaleDateString('mr-IN')}</span>\n          </div>\n        </div>\n\n        {/* View Button */}\n        <motion.div\n          whileHover={{ scale: 1.05 }}\n          whileTap={{ scale: 0.95 }}\n          className=\"mt-4 pt-4 border-t border-gray-100\"\n        >\n          <button className=\"w-full bg-orange-600 text-white py-2 px-4 rounded-lg font-semibold hover:bg-orange-700 transition-colors duration-200 flex items-center justify-center space-x-2\">\n            <Eye className=\"h-4 w-4\" />\n            <span className=\"marathi-text\">विस्तार पहा</span>\n          </button>\n        </motion.div>\n      </div>\n    </motion.div>\n  );\n};\n\nexport default GalleryItem;\n"], "names": [], "mappings": "AAAA;;;;CAIC;;;;AAID;AACA;AAAA;AAAA;AAAA;AAAA;AACA;AAJA;;;;;AAwBA,MAAM,cAAc,CAAC,EAAE,IAAI,EAAE,OAAO,EAAoB;IACtD,MAAM,mBAAmB,CAAC;QACxB,MAAM,SAAS;YACb,eAAe;gBAAE,OAAO;gBAAkB,OAAO;YAAc;YAC/D,mBAAmB;gBAAE,OAAO;gBAAkB,OAAO;YAAgB;YACrE,WAAW;gBAAE,OAAO;gBAAiB,OAAO;YAAa;YACzD,oBAAoB;gBAAE,OAAO;gBAAmB,OAAO;YAAe;YACtE,gBAAgB;gBAAE,OAAO;gBAAe,OAAO;YAAgB;QACjE;QACA,OAAO,MAAM,CAAC,SAAS,IAAI;YAAE,OAAO;YAAU,OAAO;QAAc;IACrE;IAEA,MAAM,gBAAgB,iBAAiB,KAAK,QAAQ;IAEpD,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;QACT,YAAY;YAAE,GAAG,CAAC;QAAE;QACpB,UAAU;YAAE,OAAO;QAAK;QACxB,WAAU;QACV,SAAS;;0BAGT,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,6HAAA,CAAA,UAAK;wBACJ,KAAK,KAAK,SAAS;wBACnB,KAAK,KAAK,KAAK;wBACf,IAAI;wBACJ,WAAU;;;;;;kCAIZ,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,OAAO;gCAAG,SAAS;4BAAE;4BAChC,YAAY;gCAAE,OAAO;gCAAG,SAAS;4BAAE;4BACnC,WAAU;sCAET,KAAK,IAAI,KAAK,wBACb,8OAAC,kMAAA,CAAA,OAAI;gCAAC,WAAU;;;;;yFAEhB,8OAAC,gMAAA,CAAA,MAAG;gCAAC,WAAU;;;;;;;;;;;;;;;;kCAMrB,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAW,CAAC,oFAAoF,EACnG,KAAK,IAAI,KAAK,UAAU,eAAe,eACvC;;gCACC,KAAK,IAAI,KAAK,wBACb,8OAAC,kMAAA,CAAA,OAAI;oCAAC,WAAU;;;;;6FAEhB,8OAAC,sMAAA,CAAA,SAAM;oCAAC,WAAU;;;;;;8CAEpB,8OAAC;8CAAM,KAAK,IAAI,KAAK,UAAU,YAAY;;;;;;;;;;;;;;;;;kCAK/C,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAW,CAAC,wDAAwD,EAAE,cAAc,KAAK,EAAE;sCAC7F,cAAc,KAAK;;;;;;;;;;;;;;;;;0BAM1B,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAG,WAAU;kCACX,KAAK,KAAK;;;;;;kCAEb,8OAAC;wBAAG,WAAU;kCACX,KAAK,YAAY;;;;;;kCAIpB,8OAAC;wBAAE,WAAU;kCACV,KAAK,WAAW;;;;;;kCAEnB,8OAAC;wBAAE,WAAU;kCACV,KAAK,kBAAkB;;;;;;kCAI1B,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,0MAAA,CAAA,SAAM;wCAAC,WAAU;;;;;;kDAClB,8OAAC;wCAAK,WAAU;kDAAgB,KAAK,QAAQ;;;;;;;;;;;;0CAE/C,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,0MAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;kDACpB,8OAAC;kDAAM,IAAI,KAAK,KAAK,IAAI,EAAE,kBAAkB,CAAC;;;;;;;;;;;;;;;;;;kCAKlD,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,YAAY;4BAAE,OAAO;wBAAK;wBAC1B,UAAU;4BAAE,OAAO;wBAAK;wBACxB,WAAU;kCAEV,cAAA,8OAAC;4BAAO,WAAU;;8CAChB,8OAAC,gMAAA,CAAA,MAAG;oCAAC,WAAU;;;;;;8CACf,8OAAC;oCAAK,WAAU;8CAAe;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAM3C;uCAEe", "debugId": null}}, {"offset": {"line": 1020, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Chava%20Electricals/chava-electricals/src/components/gallery/GalleryModal.tsx"], "sourcesContent": ["/*\n * Chava Electricals Website - Gallery Modal Component\n * Copyright (c) 2025 Eria Software Solutions and Services Pvt Ltd\n * All rights reserved.\n */\n\n'use client';\n\nimport { useEffect } from 'react';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport { X, MapPin, Calendar, Play, Camera, Phone, Share2 } from 'lucide-react';\nimport Image from 'next/image';\n\ninterface GalleryModalProps {\n  item: any;\n  isOpen: boolean;\n  onClose: () => void;\n}\n\nconst GalleryModal = ({ item, isOpen, onClose }: GalleryModalProps) => {\n  useEffect(() => {\n    if (isOpen) {\n      document.body.style.overflow = 'hidden';\n    } else {\n      document.body.style.overflow = 'unset';\n    }\n\n    return () => {\n      document.body.style.overflow = 'unset';\n    };\n  }, [isOpen]);\n\n  useEffect(() => {\n    const handleEscape = (e: KeyboardEvent) => {\n      if (e.key === 'Escape') {\n        onClose();\n      }\n    };\n\n    if (isOpen) {\n      document.addEventListener('keydown', handleEscape);\n    }\n\n    return () => {\n      document.removeEventListener('keydown', handleEscape);\n    };\n  }, [isOpen, onClose]);\n\n  if (!item) return null;\n\n  const getCategoryBadge = (category: string) => {\n    const badges = {\n      'home-wiring': { label: 'घरगुती वायरिंग', color: 'bg-blue-500' },\n      'sensor-lighting': { label: 'सेन्सर लाइटिंग', color: 'bg-yellow-500' },\n      'repairs': { label: 'रिपेअर सर्विस', color: 'bg-red-500' },\n      'fan-installation': { label: 'फॅन इन्स्टॉलेशन', color: 'bg-green-500' },\n      'switch-board': { label: 'स्विच बोर्ड', color: 'bg-purple-500' },\n    };\n    return badges[category] || { label: category, color: 'bg-gray-500' };\n  };\n\n  const categoryBadge = getCategoryBadge(item.category);\n\n  return (\n    <AnimatePresence>\n      {isOpen && (\n        <motion.div\n          initial={{ opacity: 0 }}\n          animate={{ opacity: 1 }}\n          exit={{ opacity: 0 }}\n          className=\"fixed inset-0 z-50 flex items-center justify-center p-4\"\n          onClick={onClose}\n        >\n          {/* Backdrop */}\n          <div className=\"absolute inset-0 bg-black/80 backdrop-blur-sm\" />\n\n          {/* Modal Content */}\n          <motion.div\n            initial={{ scale: 0.8, opacity: 0 }}\n            animate={{ scale: 1, opacity: 1 }}\n            exit={{ scale: 0.8, opacity: 0 }}\n            transition={{ type: \"spring\", damping: 25, stiffness: 300 }}\n            className=\"relative bg-white rounded-2xl max-w-4xl w-full max-h-[90vh] overflow-hidden shadow-2xl\"\n            onClick={(e) => e.stopPropagation()}\n          >\n            {/* Close Button */}\n            <button\n              onClick={onClose}\n              className=\"absolute top-4 right-4 z-10 bg-black/50 text-white p-2 rounded-full hover:bg-black/70 transition-colors duration-200\"\n            >\n              <X className=\"h-6 w-6\" />\n            </button>\n\n            <div className=\"flex flex-col lg:flex-row h-full\">\n              {/* Media Section */}\n              <div className=\"lg:w-2/3 relative\">\n                <div className=\"aspect-[4/3] lg:aspect-auto lg:h-full relative\">\n                  {item.type === 'video' ? (\n                    <div className=\"w-full h-full bg-black flex items-center justify-center\">\n                      <div className=\"text-center text-white\">\n                        <Play className=\"h-16 w-16 mx-auto mb-4\" />\n                        <p className=\"text-lg marathi-text\">व्हिडिओ प्लेयर येथे असेल</p>\n                        <p className=\"text-sm opacity-75\">Video player would be here</p>\n                      </div>\n                    </div>\n                  ) : (\n                    <Image\n                      src={item.image}\n                      alt={item.title}\n                      fill\n                      className=\"object-cover\"\n                    />\n                  )}\n                </div>\n\n                {/* Media Type Badge */}\n                <div className=\"absolute top-4 left-4\">\n                  <div className={`flex items-center space-x-1 px-3 py-1 rounded-full text-white text-sm font-semibold ${\n                    item.type === 'video' ? 'bg-red-500' : 'bg-blue-500'\n                  }`}>\n                    {item.type === 'video' ? (\n                      <Play className=\"h-4 w-4\" />\n                    ) : (\n                      <Camera className=\"h-4 w-4\" />\n                    )}\n                    <span>{item.type === 'video' ? 'व्हिडिओ' : 'फोटो'}</span>\n                  </div>\n                </div>\n              </div>\n\n              {/* Content Section */}\n              <div className=\"lg:w-1/3 p-6 lg:p-8 overflow-y-auto\">\n                {/* Category Badge */}\n                <div className=\"mb-4\">\n                  <span className={`inline-block px-3 py-1 rounded-full text-white text-sm font-semibold ${categoryBadge.color}`}>\n                    {categoryBadge.label}\n                  </span>\n                </div>\n\n                {/* Title */}\n                <h2 className=\"text-2xl font-bold text-gray-900 mb-2 marathi-text\">\n                  {item.title}\n                </h2>\n                <h3 className=\"text-xl font-semibold text-orange-600 mb-4\">\n                  {item.englishTitle}\n                </h3>\n\n                {/* Description */}\n                <div className=\"mb-6\">\n                  <p className=\"text-gray-700 mb-3 marathi-text leading-relaxed\">\n                    {item.description}\n                  </p>\n                  <p className=\"text-gray-600 text-sm leading-relaxed\">\n                    {item.englishDescription}\n                  </p>\n                </div>\n\n                {/* Meta Information */}\n                <div className=\"space-y-3 mb-6\">\n                  <div className=\"flex items-center space-x-2 text-gray-600\">\n                    <MapPin className=\"h-5 w-5 text-orange-600\" />\n                    <span className=\"marathi-text\">{item.location}</span>\n                  </div>\n                  <div className=\"flex items-center space-x-2 text-gray-600\">\n                    <Calendar className=\"h-5 w-5 text-orange-600\" />\n                    <span>{new Date(item.date).toLocaleDateString('mr-IN', {\n                      year: 'numeric',\n                      month: 'long',\n                      day: 'numeric'\n                    })}</span>\n                  </div>\n                </div>\n\n                {/* Action Buttons */}\n                <div className=\"space-y-3\">\n                  <button className=\"w-full bg-orange-600 text-white py-3 px-4 rounded-lg font-semibold hover:bg-orange-700 transition-colors duration-200 flex items-center justify-center space-x-2\">\n                    <Phone className=\"h-5 w-5\" />\n                    <span className=\"marathi-text\">अशाच कामासाठी कॉल करा</span>\n                  </button>\n                  \n                  <button className=\"w-full border-2 border-orange-600 text-orange-600 py-3 px-4 rounded-lg font-semibold hover:bg-orange-50 transition-colors duration-200 flex items-center justify-center space-x-2\">\n                    <Share2 className=\"h-5 w-5\" />\n                    <span className=\"marathi-text\">शेअर करा</span>\n                  </button>\n                </div>\n\n                {/* Additional Info */}\n                <div className=\"mt-6 pt-6 border-t border-gray-200\">\n                  <h4 className=\"font-semibold text-gray-900 mb-2 marathi-text\">\n                    या प्रकल्पाबद्दल\n                  </h4>\n                  <ul className=\"text-sm text-gray-600 space-y-1 marathi-text\">\n                    <li>• व्यावसायिक इन्स्टॉलेशन</li>\n                    <li>• गुणवत्ता हमी</li>\n                    <li>• वेळेवर पूर्ण</li>\n                    <li>• ग्राहक समाधान</li>\n                  </ul>\n                </div>\n              </div>\n            </div>\n          </motion.div>\n        </motion.div>\n      )}\n    </AnimatePresence>\n  );\n};\n\nexport default GalleryModal;\n"], "names": [], "mappings": "AAAA;;;;CAIC;;;;AAID;AACA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AALA;;;;;;AAaA,MAAM,eAAe,CAAC,EAAE,IAAI,EAAE,MAAM,EAAE,OAAO,EAAqB;IAChE,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,QAAQ;YACV,SAAS,IAAI,CAAC,KAAK,CAAC,QAAQ,GAAG;QACjC,OAAO;YACL,SAAS,IAAI,CAAC,KAAK,CAAC,QAAQ,GAAG;QACjC;QAEA,OAAO;YACL,SAAS,IAAI,CAAC,KAAK,CAAC,QAAQ,GAAG;QACjC;IACF,GAAG;QAAC;KAAO;IAEX,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,eAAe,CAAC;YACpB,IAAI,EAAE,GAAG,KAAK,UAAU;gBACtB;YACF;QACF;QAEA,IAAI,QAAQ;YACV,SAAS,gBAAgB,CAAC,WAAW;QACvC;QAEA,OAAO;YACL,SAAS,mBAAmB,CAAC,WAAW;QAC1C;IACF,GAAG;QAAC;QAAQ;KAAQ;IAEpB,IAAI,CAAC,MAAM,OAAO;IAElB,MAAM,mBAAmB,CAAC;QACxB,MAAM,SAAS;YACb,eAAe;gBAAE,OAAO;gBAAkB,OAAO;YAAc;YAC/D,mBAAmB;gBAAE,OAAO;gBAAkB,OAAO;YAAgB;YACrE,WAAW;gBAAE,OAAO;gBAAiB,OAAO;YAAa;YACzD,oBAAoB;gBAAE,OAAO;gBAAmB,OAAO;YAAe;YACtE,gBAAgB;gBAAE,OAAO;gBAAe,OAAO;YAAgB;QACjE;QACA,OAAO,MAAM,CAAC,SAAS,IAAI;YAAE,OAAO;YAAU,OAAO;QAAc;IACrE;IAEA,MAAM,gBAAgB,iBAAiB,KAAK,QAAQ;IAEpD,qBACE,8OAAC,yLAAA,CAAA,kBAAe;kBACb,wBACC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;YACT,SAAS;gBAAE,SAAS;YAAE;YACtB,SAAS;gBAAE,SAAS;YAAE;YACtB,MAAM;gBAAE,SAAS;YAAE;YACnB,WAAU;YACV,SAAS;;8BAGT,8OAAC;oBAAI,WAAU;;;;;;8BAGf,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,OAAO;wBAAK,SAAS;oBAAE;oBAClC,SAAS;wBAAE,OAAO;wBAAG,SAAS;oBAAE;oBAChC,MAAM;wBAAE,OAAO;wBAAK,SAAS;oBAAE;oBAC/B,YAAY;wBAAE,MAAM;wBAAU,SAAS;wBAAI,WAAW;oBAAI;oBAC1D,WAAU;oBACV,SAAS,CAAC,IAAM,EAAE,eAAe;;sCAGjC,8OAAC;4BACC,SAAS;4BACT,WAAU;sCAEV,cAAA,8OAAC,4LAAA,CAAA,IAAC;gCAAC,WAAU;;;;;;;;;;;sCAGf,8OAAC;4BAAI,WAAU;;8CAEb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACZ,KAAK,IAAI,KAAK,wBACb,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAI,WAAU;;sEACb,8OAAC,kMAAA,CAAA,OAAI;4DAAC,WAAU;;;;;;sEAChB,8OAAC;4DAAE,WAAU;sEAAuB;;;;;;sEACpC,8OAAC;4DAAE,WAAU;sEAAqB;;;;;;;;;;;;;;;;yGAItC,8OAAC,6HAAA,CAAA,UAAK;gDACJ,KAAK,KAAK,KAAK;gDACf,KAAK,KAAK,KAAK;gDACf,IAAI;gDACJ,WAAU;;;;;;;;;;;sDAMhB,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAI,WAAW,CAAC,oFAAoF,EACnG,KAAK,IAAI,KAAK,UAAU,eAAe,eACvC;;oDACC,KAAK,IAAI,KAAK,wBACb,8OAAC,kMAAA,CAAA,OAAI;wDAAC,WAAU;;;;;iHAEhB,8OAAC,sMAAA,CAAA,SAAM;wDAAC,WAAU;;;;;;kEAEpB,8OAAC;kEAAM,KAAK,IAAI,KAAK,UAAU,YAAY;;;;;;;;;;;;;;;;;;;;;;;8CAMjD,8OAAC;oCAAI,WAAU;;sDAEb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAK,WAAW,CAAC,qEAAqE,EAAE,cAAc,KAAK,EAAE;0DAC3G,cAAc,KAAK;;;;;;;;;;;sDAKxB,8OAAC;4CAAG,WAAU;sDACX,KAAK,KAAK;;;;;;sDAEb,8OAAC;4CAAG,WAAU;sDACX,KAAK,YAAY;;;;;;sDAIpB,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAE,WAAU;8DACV,KAAK,WAAW;;;;;;8DAEnB,8OAAC;oDAAE,WAAU;8DACV,KAAK,kBAAkB;;;;;;;;;;;;sDAK5B,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;sEACb,8OAAC,0MAAA,CAAA,SAAM;4DAAC,WAAU;;;;;;sEAClB,8OAAC;4DAAK,WAAU;sEAAgB,KAAK,QAAQ;;;;;;;;;;;;8DAE/C,8OAAC;oDAAI,WAAU;;sEACb,8OAAC,0MAAA,CAAA,WAAQ;4DAAC,WAAU;;;;;;sEACpB,8OAAC;sEAAM,IAAI,KAAK,KAAK,IAAI,EAAE,kBAAkB,CAAC,SAAS;gEACrD,MAAM;gEACN,OAAO;gEACP,KAAK;4DACP;;;;;;;;;;;;;;;;;;sDAKJ,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAO,WAAU;;sEAChB,8OAAC,oMAAA,CAAA,QAAK;4DAAC,WAAU;;;;;;sEACjB,8OAAC;4DAAK,WAAU;sEAAe;;;;;;;;;;;;8DAGjC,8OAAC;oDAAO,WAAU;;sEAChB,8OAAC,0MAAA,CAAA,SAAM;4DAAC,WAAU;;;;;;sEAClB,8OAAC;4DAAK,WAAU;sEAAe;;;;;;;;;;;;;;;;;;sDAKnC,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAG,WAAU;8DAAgD;;;;;;8DAG9D,8OAAC;oDAAG,WAAU;;sEACZ,8OAAC;sEAAG;;;;;;sEACJ,8OAAC;sEAAG;;;;;;sEACJ,8OAAC;sEAAG;;;;;;sEACJ,8OAAC;sEAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUxB;uCAEe", "debugId": null}}, {"offset": {"line": 1529, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Chava%20Electricals/chava-electricals/src/components/gallery/GalleryGrid.tsx"], "sourcesContent": ["/*\n * Chava Electricals Website - Gallery Grid Component\n * Copyright (c) 2025 Eria Software Solutions and Services Pvt Ltd\n * All rights reserved.\n */\n\n'use client';\n\nimport { useState, useEffect } from 'react';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport { useInView } from 'react-intersection-observer';\nimport GalleryItem from './GalleryItem';\nimport GalleryModal from './GalleryModal';\n\n// Sample gallery data - In a real application, this would come from a CMS or API\nconst galleryData = [\n  {\n    id: 1,\n    type: 'photo',\n    category: 'home-wiring',\n    title: 'संपूर्ण घर वायरिंग - पुणे',\n    description: '3BHK घरासाठी संपूर्ण इलेक्ट्रिकल वायरिंग प्रकल्प',\n    location: 'पुणे',\n    date: '2024-12-15',\n    image: '/api/placeholder/400/300',\n    thumbnail: '/api/placeholder/400/300'\n  },\n  {\n    id: 2,\n    type: 'video',\n    category: 'sensor-lighting',\n    title: 'सेन्सर लाइट इन्स्टॉलेशन',\n    description: 'ऑटोमॅटिक सेन्सर लाइट्स बसवणे',\n    location: 'नाशिक',\n    date: '2024-12-10',\n    image: '/api/placeholder/400/300',\n    videoUrl: '/api/placeholder/video',\n    thumbnail: '/api/placeholder/400/300'\n  },\n  {\n    id: 3,\n    type: 'photo',\n    category: 'switch-board',\n    title: 'मॉड्यूलर स्विच बोर्ड',\n    description: 'आधुनिक मॉड्यूलर स्विच बोर्ड इन्स्टॉलेशन',\n    location: 'मुंबई',\n    date: '2024-12-08',\n    image: '/api/placeholder/400/300',\n    thumbnail: '/api/placeholder/400/300'\n  },\n  {\n    id: 4,\n    type: 'photo',\n    category: 'fan-installation',\n    title: 'सीलिंग फॅन इन्स्टॉलेशन',\n    description: 'हाय स्पीड सीलिंग फॅन बसवणे',\n    location: 'कोल्हापूर',\n    date: '2024-12-05',\n    image: '/api/placeholder/400/300',\n    thumbnail: '/api/placeholder/400/300'\n  },\n  {\n    id: 5,\n    type: 'video',\n    category: 'repairs',\n    title: 'इमर्जन्सी रिपेअर सर्विस',\n    description: 'रात्रीच्या वेळी इमर्जन्सी रिपेअर',\n    location: 'औरंगाबाद',\n    date: '2024-12-03',\n    image: '/api/placeholder/400/300',\n    videoUrl: '/api/placeholder/video',\n    thumbnail: '/api/placeholder/400/300'\n  },\n  {\n    id: 6,\n    type: 'photo',\n    category: 'home-wiring',\n    title: 'नवीन घर वायरिंग',\n    description: 'नवीन बांधकामातील वायरिंग काम',\n    location: 'सांगली',\n    date: '2024-12-01',\n    image: '/api/placeholder/400/300',\n    thumbnail: '/api/placeholder/400/300'\n  }\n];\n\ninterface GalleryGridProps {\n  activeFilter?: string;\n  activeMediaType?: string;\n}\n\nconst GalleryGrid = ({ activeFilter = 'all', activeMediaType = 'all' }: GalleryGridProps = {}) => {\n  const [filteredData, setFilteredData] = useState(galleryData);\n  const [selectedItem, setSelectedItem] = useState<any>(null);\n  const [ref, inView] = useInView({\n    triggerOnce: true,\n    threshold: 0.1\n  });\n\n  useEffect(() => {\n    let filtered = galleryData;\n\n    // Filter by category\n    if (activeFilter !== 'all') {\n      filtered = filtered.filter(item => item.category === activeFilter);\n    }\n\n    // Filter by media type\n    if (activeMediaType !== 'all') {\n      if (activeMediaType === 'photos') {\n        filtered = filtered.filter(item => item.type === 'photo');\n      } else if (activeMediaType === 'videos') {\n        filtered = filtered.filter(item => item.type === 'video');\n      }\n    }\n\n    setFilteredData(filtered);\n  }, [activeFilter, activeMediaType]);\n\n  const containerVariants = {\n    hidden: { opacity: 0 },\n    visible: {\n      opacity: 1,\n      transition: {\n        staggerChildren: 0.1\n      }\n    }\n  };\n\n  const itemVariants = {\n    hidden: { opacity: 0, y: 50 },\n    visible: {\n      opacity: 1,\n      y: 0,\n      transition: {\n        duration: 0.6\n      }\n    }\n  };\n\n  return (\n    <>\n      <motion.div\n        ref={ref}\n        initial=\"hidden\"\n        animate={inView ? \"visible\" : \"hidden\"}\n        variants={containerVariants}\n        className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8\"\n      >\n        <AnimatePresence mode=\"wait\">\n          {filteredData.map((item) => (\n            <motion.div\n              key={item.id}\n              variants={itemVariants}\n              layout\n              initial={{ opacity: 0, scale: 0.8 }}\n              animate={{ opacity: 1, scale: 1 }}\n              exit={{ opacity: 0, scale: 0.8 }}\n              transition={{ duration: 0.3 }}\n            >\n              <GalleryItem\n                item={item}\n                onClick={() => setSelectedItem(item)}\n              />\n            </motion.div>\n          ))}\n        </AnimatePresence>\n      </motion.div>\n\n      {filteredData.length === 0 && (\n        <motion.div\n          initial={{ opacity: 0, y: 50 }}\n          animate={{ opacity: 1, y: 0 }}\n          className=\"text-center py-16\"\n        >\n          <div className=\"text-gray-400 mb-4\">\n            <svg className=\"w-16 h-16 mx-auto\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M9.172 16.172a4 4 0 015.656 0M9 12h6m-6-4h6m2 5.291A7.962 7.962 0 0112 15c-2.34 0-4.47-.881-6.08-2.33\" />\n            </svg>\n          </div>\n          <h3 className=\"text-xl font-semibold text-gray-600 mb-2 marathi-text\">\n            या फिल्टरसाठी कोणतेही परिणाम सापडले नाहीत\n          </h3>\n         \n        </motion.div>\n      )}\n\n      {/* Gallery Modal */}\n      <GalleryModal\n        item={selectedItem}\n        isOpen={!!selectedItem}\n        onClose={() => setSelectedItem(null)}\n      />\n    </>\n  );\n};\n\nexport default GalleryGrid;\n"], "names": [], "mappings": "AAAA;;;;CAIC;;;;AAID;AACA;AAAA;AACA;AACA;AACA;AANA;;;;;;;AAQA,iFAAiF;AACjF,MAAM,cAAc;IAClB;QACE,IAAI;QACJ,MAAM;QACN,UAAU;QACV,OAAO;QACP,aAAa;QACb,UAAU;QACV,MAAM;QACN,OAAO;QACP,WAAW;IACb;IACA;QACE,IAAI;QACJ,MAAM;QACN,UAAU;QACV,OAAO;QACP,aAAa;QACb,UAAU;QACV,MAAM;QACN,OAAO;QACP,UAAU;QACV,WAAW;IACb;IACA;QACE,IAAI;QACJ,MAAM;QACN,UAAU;QACV,OAAO;QACP,aAAa;QACb,UAAU;QACV,MAAM;QACN,OAAO;QACP,WAAW;IACb;IACA;QACE,IAAI;QACJ,MAAM;QACN,UAAU;QACV,OAAO;QACP,aAAa;QACb,UAAU;QACV,MAAM;QACN,OAAO;QACP,WAAW;IACb;IACA;QACE,IAAI;QACJ,MAAM;QACN,UAAU;QACV,OAAO;QACP,aAAa;QACb,UAAU;QACV,MAAM;QACN,OAAO;QACP,UAAU;QACV,WAAW;IACb;IACA;QACE,IAAI;QACJ,MAAM;QACN,UAAU;QACV,OAAO;QACP,aAAa;QACb,UAAU;QACV,MAAM;QACN,OAAO;QACP,WAAW;IACb;CACD;AAOD,MAAM,cAAc,CAAC,EAAE,eAAe,KAAK,EAAE,kBAAkB,KAAK,EAAoB,GAAG,CAAC,CAAC;IAC3F,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAO;IACtD,MAAM,CAAC,KAAK,OAAO,GAAG,CAAA,GAAA,mKAAA,CAAA,YAAS,AAAD,EAAE;QAC9B,aAAa;QACb,WAAW;IACb;IAEA,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,WAAW;QAEf,qBAAqB;QACrB,IAAI,iBAAiB,OAAO;YAC1B,WAAW,SAAS,MAAM,CAAC,CAAA,OAAQ,KAAK,QAAQ,KAAK;QACvD;QAEA,uBAAuB;QACvB,IAAI,oBAAoB,OAAO;YAC7B,IAAI,oBAAoB,UAAU;gBAChC,WAAW,SAAS,MAAM,CAAC,CAAA,OAAQ,KAAK,IAAI,KAAK;YACnD,OAAO,IAAI,oBAAoB,UAAU;gBACvC,WAAW,SAAS,MAAM,CAAC,CAAA,OAAQ,KAAK,IAAI,KAAK;YACnD;QACF;QAEA,gBAAgB;IAClB,GAAG;QAAC;QAAc;KAAgB;IAElC,MAAM,oBAAoB;QACxB,QAAQ;YAAE,SAAS;QAAE;QACrB,SAAS;YACP,SAAS;YACT,YAAY;gBACV,iBAAiB;YACnB;QACF;IACF;IAEA,MAAM,eAAe;QACnB,QAAQ;YAAE,SAAS;YAAG,GAAG;QAAG;QAC5B,SAAS;YACP,SAAS;YACT,GAAG;YACH,YAAY;gBACV,UAAU;YACZ;QACF;IACF;IAEA,qBACE;;0BACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,KAAK;gBACL,SAAQ;gBACR,SAAS,SAAS,YAAY;gBAC9B,UAAU;gBACV,WAAU;0BAEV,cAAA,8OAAC,yLAAA,CAAA,kBAAe;oBAAC,MAAK;8BACnB,aAAa,GAAG,CAAC,CAAC,qBACjB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BAET,UAAU;4BACV,MAAM;4BACN,SAAS;gCAAE,SAAS;gCAAG,OAAO;4BAAI;4BAClC,SAAS;gCAAE,SAAS;gCAAG,OAAO;4BAAE;4BAChC,MAAM;gCAAE,SAAS;gCAAG,OAAO;4BAAI;4BAC/B,YAAY;gCAAE,UAAU;4BAAI;sCAE5B,cAAA,8OAAC,4IAAA,CAAA,UAAW;gCACV,MAAM;gCACN,SAAS,IAAM,gBAAgB;;;;;;2BAV5B,KAAK,EAAE;;;;;;;;;;;;;;;YAiBnB,aAAa,MAAM,KAAK,mBACvB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,SAAS;oBAAE,SAAS;oBAAG,GAAG;gBAAG;gBAC7B,SAAS;oBAAE,SAAS;oBAAG,GAAG;gBAAE;gBAC5B,WAAU;;kCAEV,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;4BAAoB,MAAK;4BAAO,QAAO;4BAAe,SAAQ;sCAC3E,cAAA,8OAAC;gCAAK,eAAc;gCAAQ,gBAAe;gCAAQ,aAAa;gCAAG,GAAE;;;;;;;;;;;;;;;;kCAGzE,8OAAC;wBAAG,WAAU;kCAAwD;;;;;;;;;;;;0BAQ1E,8OAAC,6IAAA,CAAA,UAAY;gBACX,MAAM;gBACN,QAAQ,CAAC,CAAC;gBACV,SAAS,IAAM,gBAAgB;;;;;;;;AAIvC;uCAEe", "debugId": null}}, {"offset": {"line": 1791, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Chava%20Electricals/chava-electricals/src/app/gallery/page.tsx"], "sourcesContent": ["/*\n * Chava Electricals Website - Gallery Page\n * Copyright (c) 2025 Eria Software Solutions and Services Pvt Ltd\n * All rights reserved.\n */\n\n'use client';\n\nimport { useState } from 'react';\nimport GalleryHero from '@/components/gallery/GalleryHero';\nimport GalleryFilter from '@/components/gallery/GalleryFilter';\nimport GalleryGrid from '@/components/gallery/GalleryGrid';\n\nexport default function GalleryPage() {\n  const [activeFilter, setActiveFilter] = useState('all');\n  const [activeMediaType, setActiveMediaType] = useState('all');\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      <GalleryHero />\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12\">\n        <GalleryFilter\n          onFilterChange={setActiveFilter}\n          onMediaTypeChange={setActiveMediaType}\n        />\n        <GalleryGrid\n          activeFilter={activeFilter}\n          activeMediaType={activeMediaType}\n        />\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": "AAAA;;;;CAIC;;;;AAID;AACA;AACA;AACA;AALA;;;;;;AAOe,SAAS;IACtB,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEvD,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC,4IAAA,CAAA,UAAW;;;;;0BACZ,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,8IAAA,CAAA,UAAa;wBACZ,gBAAgB;wBAChB,mBAAmB;;;;;;kCAErB,8OAAC,4IAAA,CAAA,UAAW;wBACV,cAAc;wBACd,iBAAiB;;;;;;;;;;;;;;;;;;AAK3B", "debugId": null}}]}