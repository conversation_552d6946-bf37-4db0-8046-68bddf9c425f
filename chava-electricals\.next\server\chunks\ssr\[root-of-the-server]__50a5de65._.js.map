{"version": 3, "sources": [], "sections": [{"offset": {"line": 37, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Chava%20Electricals/chava-electricals/src/components/Navbar.tsx"], "sourcesContent": ["/*\n * Chava Electricals Website - Navigation Component\n * Copyright (c) 2025 Eria Software Solutions and Services Pvt Ltd\n * All rights reserved.\n */\n\n'use client';\n\nimport { useState } from 'react';\nimport Link from 'next/link';\nimport { Menu, X, Zap, Phone } from 'lucide-react';\nimport { motion, AnimatePresence } from 'framer-motion';\n\nconst Navbar = () => {\n  const [isOpen, setIsOpen] = useState(false);\n\n  const navItems = [\n    { href: '/', label: 'मुख्यपृष्ठ' },\n    { href: '/services', label: 'सेवा' },\n    { href: '/gallery', label: 'आमचे काम' },\n    { href: '/store', label: 'ऑनलाइन स्टोअर' },\n    { href: '/about', label: 'आमच्याबद्दल' },\n    { href: '/contact', label: 'संपर्क' },\n  ];\n\n  return (\n    <nav className=\"bg-gradient-to-r from-blue-700 via-teal-600 to-green-600 shadow-lg sticky top-0 z-50\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        <div className=\"flex justify-between items-center h-16\">\n          {/* Logo */}\n          <Link href=\"/\" className=\"flex items-center space-x-2\">\n            <div className=\"bg-white p-2 rounded-full\">\n              <Zap className=\"h-6 w-6 text-blue-700\" />\n            </div>\n            <div className=\"text-white\">\n              <h1 className=\"text-xl font-bold marathi-text\">छावा इलेक्ट्रिकल्स</h1>\n            </div>\n          </Link>\n\n          {/* Desktop Navigation */}\n          <div className=\"hidden md:flex items-center space-x-8\">\n            {navItems.map((item) => (\n              <Link\n                key={item.href}\n                href={item.href}\n                className=\"text-white hover:text-blue-100 transition-colors duration-200 font-medium marathi-text\"\n              >\n                {item.label}\n              </Link>\n            ))}\n            \n            {/* Call Button */}\n            <Link\n              href=\"tel:+919876543210\"\n              className=\"bg-white text-blue-700 px-4 py-2 rounded-full font-semibold hover:bg-blue-50 transition-colors duration-200 flex items-center space-x-2\"\n            >\n              <Phone className=\"h-4 w-4\" />\n              <span className=\"marathi-text\">कॉल करा</span>\n            </Link>\n          </div>\n\n          {/* Mobile menu button */}\n          <div className=\"md:hidden\">\n            <button\n              onClick={() => setIsOpen(!isOpen)}\n              className=\"text-white hover:text-blue-100 transition-colors duration-200\"\n            >\n              {isOpen ? <X className=\"h-6 w-6\" /> : <Menu className=\"h-6 w-6\" />}\n            </button>\n          </div>\n        </div>\n      </div>\n\n      {/* Mobile Navigation */}\n      <AnimatePresence>\n        {isOpen && (\n          <motion.div\n            initial={{ opacity: 0, height: 0 }}\n            animate={{ opacity: 1, height: 'auto' }}\n            exit={{ opacity: 0, height: 0 }}\n            className=\"md:hidden bg-blue-800\"\n          >\n            <div className=\"px-2 pt-2 pb-3 space-y-1\">\n              {navItems.map((item) => (\n                <Link\n                  key={item.href}\n                  href={item.href}\n                  className=\"block px-3 py-2 text-white hover:bg-blue-700 rounded-md transition-colors duration-200 marathi-text\"\n                  onClick={() => setIsOpen(false)}\n                >\n                  {item.label}\n                </Link>\n              ))}\n              <Link\n                href=\"tel:+919876543210\"\n                className=\"block px-3 py-2 bg-white text-blue-700 rounded-md font-semibold hover:bg-blue-50 transition-colors duration-200 flex items-center space-x-2\"\n                onClick={() => setIsOpen(false)}\n              >\n                <Phone className=\"h-4 w-4\" />\n                <span className=\"marathi-text\">कॉल करा</span>\n              </Link>\n            </div>\n          </motion.div>\n        )}\n      </AnimatePresence>\n    </nav>\n  );\n};\n\nexport default Navbar;\n"], "names": [], "mappings": "AAAA;;;;CAIC;;;;AAID;AACA;AACA;AAAA;AAAA;AAAA;AACA;AAAA;AALA;;;;;;AAOA,MAAM,SAAS;IACb,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAErC,MAAM,WAAW;QACf;YAAE,MAAM;YAAK,OAAO;QAAa;QACjC;YAAE,MAAM;YAAa,OAAO;QAAO;QACnC;YAAE,MAAM;YAAY,OAAO;QAAW;QACtC;YAAE,MAAM;YAAU,OAAO;QAAgB;QACzC;YAAE,MAAM;YAAU,OAAO;QAAc;QACvC;YAAE,MAAM;YAAY,OAAO;QAAS;KACrC;IAED,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC,4JAAA,CAAA,UAAI;4BAAC,MAAK;4BAAI,WAAU;;8CACvB,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC,gMAAA,CAAA,MAAG;wCAAC,WAAU;;;;;;;;;;;8CAEjB,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAG,WAAU;kDAAiC;;;;;;;;;;;;;;;;;sCAKnD,8OAAC;4BAAI,WAAU;;gCACZ,SAAS,GAAG,CAAC,CAAC,qBACb,8OAAC,4JAAA,CAAA,UAAI;wCAEH,MAAM,KAAK,IAAI;wCACf,WAAU;kDAET,KAAK,KAAK;uCAJN,KAAK,IAAI;;;;;8CASlB,8OAAC,4JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;;sDAEV,8OAAC,oMAAA,CAAA,QAAK;4CAAC,WAAU;;;;;;sDACjB,8OAAC;4CAAK,WAAU;sDAAe;;;;;;;;;;;;;;;;;;sCAKnC,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCACC,SAAS,IAAM,UAAU,CAAC;gCAC1B,WAAU;0CAET,uBAAS,8OAAC,4LAAA,CAAA,IAAC;oCAAC,WAAU;;;;;6FAAe,8OAAC,kMAAA,CAAA,OAAI;oCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAO9D,8OAAC,yLAAA,CAAA,kBAAe;0BACb,wBACC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,QAAQ;oBAAE;oBACjC,SAAS;wBAAE,SAAS;wBAAG,QAAQ;oBAAO;oBACtC,MAAM;wBAAE,SAAS;wBAAG,QAAQ;oBAAE;oBAC9B,WAAU;8BAEV,cAAA,8OAAC;wBAAI,WAAU;;4BACZ,SAAS,GAAG,CAAC,CAAC,qBACb,8OAAC,4JAAA,CAAA,UAAI;oCAEH,MAAM,KAAK,IAAI;oCACf,WAAU;oCACV,SAAS,IAAM,UAAU;8CAExB,KAAK,KAAK;mCALN,KAAK,IAAI;;;;;0CAQlB,8OAAC,4JAAA,CAAA,UAAI;gCACH,MAAK;gCACL,WAAU;gCACV,SAAS,IAAM,UAAU;;kDAEzB,8OAAC,oMAAA,CAAA,QAAK;wCAAC,WAAU;;;;;;kDACjB,8OAAC;wCAAK,WAAU;kDAAe;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQ/C;uCAEe", "debugId": null}}]}