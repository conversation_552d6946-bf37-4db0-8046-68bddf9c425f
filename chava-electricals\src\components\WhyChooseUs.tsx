/*
 * Chava Electricals Website - Why Choose Us Component
 * Copyright (c) 2025 Eria Software Solutions and Services Pvt Ltd
 * All rights reserved.
 */

'use client';

import { motion } from 'framer-motion';
import { useInView } from 'react-intersection-observer';
import { 
  Shield, 
  Clock, 
  Award, 
  Users, 
  ThumbsUp, 
  Zap,
  CheckCircle,
  Star
} from 'lucide-react';

const WhyChooseUs = () => {
  const [ref, inView] = useInView({
    triggerOnce: true,
    threshold: 0.1
  });

  const features = [
    {
      icon: <Shield className="h-8 w-8" />,
      title: "लायसन्स प्राप्त",
      description: "सरकारी मान्यताप्राप्त आणि विमाकृत सेवा"
    },
    {
      icon: <Clock className="h-8 w-8" />,
      title: "24/7 उपलब्ध",
      description: "कधीही इमर्जन्सी सेवेसाठी उपलब्ध"
    },
    {
      icon: <Award className="h-8 w-8" />,
      title: "10+ वर्षांचा अनुभव",
      description: "दशकभराचा व्यावसायिक अनुभव"
    },
    {
      icon: <Users className="h-8 w-8" />,
      title: "तज्ञ टीम",
      description: "कुशल आणि प्रशिक्षित तंत्रज्ञ"
    },
    {
      icon: <ThumbsUp className="h-8 w-8" />,
      title: "गुणवत्ता हमी",
      description: "सर्व कामांची गुणवत्ता हमी"
    },
    {
      icon: <Zap className="h-8 w-8" />,
      title: "त्वरित सेवा",
      description: "वेळेवर आणि कार्यक्षम सेवा"
    }
  ];

  const stats = [
    {
      number: "500+",
      label: "समाधान केलेले ग्राहक",
     
    },
    {
      number: "1000+",
      label: "पूर्ण केलेले प्रकल्प",
      
    },
    {
      number: "10+",
      label: "वर्षांचा अनुभव",
     
    },
    {
      number: "24/7",
      label: "सेवा उपलब्धता",
      
    }
  ];

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.2
      }
    }
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 50 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.6
      }
    }
  };

  return (
    <section className="py-20 bg-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <motion.div
          ref={ref}
          initial="hidden"
          animate={inView ? "visible" : "hidden"}
          variants={containerVariants}
          className="text-center mb-16"
        >
          <motion.h2
            variants={itemVariants}
            className="text-4xl md:text-5xl font-bold text-gray-900 mb-4 marathi-text"
          >
            आम्हाला का निवडावे?
          </motion.h2>
          
          <motion.p
            variants={itemVariants}
            className="text-xl text-gray-600 max-w-3xl mx-auto marathi-text"
          >
            आम्ही महाराष्ट्रातील सर्वोत्तम इलेक्ट्रिकल सेवा प्रदाता आहोत
          </motion.p>
        </motion.div>

        {/* Features Grid */}
        <motion.div
          initial="hidden"
          animate={inView ? "visible" : "hidden"}
          variants={containerVariants}
          className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-16"
        >
          {features.map((feature, index) => (
            <motion.div
              key={index}
              variants={itemVariants}
              className="text-center p-6 rounded-xl bg-yellow-50 hover:bg-yellow-100 transition-all duration-300 transform hover:-translate-y-2 group"
            >
              <div className="text-yellow-600 mb-4 flex justify-center group-hover:scale-110 transition-transform duration-300">
                {feature.icon}
              </div>
              <h4 className="text-lg font-bold text-gray-900 mb-3 marathi-text">
                {feature.title}
              </h4>
              <p className="text-gray-600 marathi-text">
                {feature.description}
              </p>
            </motion.div>
          ))}
        </motion.div>

        {/* Stats Section */}
        <motion.div
          initial="hidden"
          animate={inView ? "visible" : "hidden"}
          variants={containerVariants}
          className="bg-gradient-to-r from-yellow-400 to-yellow-500 rounded-2xl p-8 md:p-12"
        >
          <div className="grid grid-cols-2 md:grid-cols-4 gap-8 text-center text-white">
            {stats.map((stat, index) => (
              <motion.div
                key={index}
                variants={itemVariants}
                className="space-y-2"
              >
                <motion.div
                  initial={{ scale: 0 }}
                  animate={inView ? { scale: 1 } : { scale: 0 }}
                  transition={{ delay: index * 0.2, type: "spring", stiffness: 200 }}
                  className="text-3xl md:text-4xl font-bold"
                >
                  {stat.number}
                </motion.div>
                <div className="text-sm md:text-base marathi-text">
                  {stat.label}
                </div>
                <div className="text-xs text-yellow-100">
                  {stat.english}
                </div>
              </motion.div>
            ))}
          </div>
        </motion.div>

        {/* Testimonial Preview */}
        <motion.div
          initial={{ opacity: 0, y: 50 }}
          animate={inView ? { opacity: 1, y: 0 } : { opacity: 0, y: 50 }}
          transition={{ delay: 1.0, duration: 0.6 }}
          className="mt-16 text-center"
        >
          <div className="bg-yellow-50 rounded-2xl p-8 max-w-4xl mx-auto">
            <div className="flex justify-center mb-4">
              {[...Array(5)].map((_, i) => (
                <Star key={i} className="h-6 w-6 text-yellow-400 fill-current" />
              ))}
            </div>
            <blockquote className="text-xl text-gray-700 mb-6 marathi-text">
              "चावा इलेक्ट्रिकल्सची सेवा खूप चांगली आहे. त्यांनी आमच्या घराचे संपूर्ण वायरिंग वेळेवर आणि गुणवत्तेने केले."
            </blockquote>
            <cite className="text-gray-600 font-semibold">
              - राजेश पाटील, पुणे
            </cite>
          </div>
        </motion.div>
      </div>
    </section>
  );
};

export default WhyChooseUs;
