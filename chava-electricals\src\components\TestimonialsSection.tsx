/*
 * Chava Electricals Website - Testimonials Section Component
 * Copyright (c) 2025 Eria Software Solutions and Services Pvt Ltd
 * All rights reserved.
 */

'use client';

import { motion } from 'framer-motion';
import { useInView } from 'react-intersection-observer';
import { Star, Quote } from 'lucide-react';

const TestimonialsSection = () => {
  const [ref, inView] = useInView({
    triggerOnce: true,
    threshold: 0.1
  });

  const testimonials = [
    {
      name: "सुनील शर्मा",
      location: "पुणे",
      rating: 5,
      text: "छावा इलेक्ट्रिकल्सने आमच्या घराचे संपूर्ण वायरिंग अतिशय व्यावसायिकतेने केले. त्यांची सेवा उत्कृष्ट आहे.",
      service: "घरगुती वायरिंग"
    },
    {
      name: "प्रिया देशमुख",
      location: "नाशिक",
      rating: 5,
      text: "सेन्सर लाइट्स लावण्यासाठी त्यांना बोलावले होते. काम अतिशय नीटनेटके आणि वेळेवर झाले.",
      service: "सेन्सर लाइटिंग"
    },
    {
      name: "अमित पाटील",
      location: "मुंबई",
      rating: 5,
      text: "इमर्जन्सी मध्ये त्यांनी लगेच मदत केली. 24/7 सेवा खरोखरच उपलब्ध आहे.",
      service: "इमर्जन्सी सर्विस"
    },
    {
      name: "मीरा जोशी",
      location: "कोल्हापूर",
      rating: 5,
      text: "फॅन इन्स्टॉलेशनचे काम उत्तम झाले. किंमत देखील योग्य होती.",
      service: "फॅन इन्स्टॉलेशन"
    },
    {
      name: "विकास कुलकर्णी",
      location: "औरंगाबाद",
      rating: 5,
      text: "स्विच बोर्ड अपग्रेड करण्यासाठी त्यांची सेवा घेतली. अतिशय समाधानकारक अनुभव.",
      service: "स्विच बोर्ड"
    },
    {
      name: "संगीता राणे",
      location: "सांगली",
      rating: 5,
      text: "सेफ्टी चेकअप केल्यानंतर घरातील सर्व इलेक्ट्रिकल समस्या सुटल्या.",
      service: "सेफ्टी चेकअप"
    }
  ];

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.2
      }
    }
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 50 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.6
      }
    }
  };

  return (
    <section className="py-20 bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <motion.div
          ref={ref}
          initial="hidden"
          animate={inView ? "visible" : "hidden"}
          variants={containerVariants}
          className="text-center mb-16"
        >
          <motion.h2
            variants={itemVariants}
            className="text-4xl md:text-5xl font-bold text-gray-900 mb-4 marathi-text"
          >
            ग्राहकांचे अनुभव
          </motion.h2>
          <motion.h3
            variants={itemVariants}
            className="text-2xl md:text-3xl font-semibold text-orange-600 mb-6"
          >
            Customer Testimonials
          </motion.h3>
          <motion.p
            variants={itemVariants}
            className="text-xl text-gray-600 max-w-3xl mx-auto marathi-text"
          >
            आमच्या समाधान झालेल्या ग्राहकांचे अनुभव वाचा
          </motion.p>
        </motion.div>

        <motion.div
          initial="hidden"
          animate={inView ? "visible" : "hidden"}
          variants={containerVariants}
          className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8"
        >
          {testimonials.map((testimonial, index) => (
            <motion.div
              key={index}
              variants={itemVariants}
              className="bg-white rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-2 p-6 relative"
            >
              {/* Quote Icon */}
              <div className="absolute top-4 right-4 text-teal-200">
                <Quote className="h-8 w-8" />
              </div>

              {/* Rating */}
              <div className="flex items-center mb-4">
                {[...Array(testimonial.rating)].map((_, i) => (
                  <Star key={i} className="h-5 w-5 text-yellow-400 fill-current" />
                ))}
              </div>

              {/* Service Badge */}
              <div className="inline-block bg-teal-100 text-teal-600 px-3 py-1 rounded-full text-sm font-semibold mb-4 marathi-text">
                {testimonial.service}
              </div>

              {/* Testimonial Text */}
              <blockquote className="text-gray-700 mb-6 marathi-text leading-relaxed">
                "{testimonial.text}"
              </blockquote>

              {/* Customer Info */}
              <div className="border-t pt-4">
                <div className="font-semibold text-gray-900 marathi-text">
                  {testimonial.name}
                </div>
                <div className="text-sm text-gray-500">
                  {testimonial.location}
                </div>
              </div>
            </motion.div>
          ))}
        </motion.div>

        {/* Call to Action */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={inView ? { opacity: 1, y: 0 } : { opacity: 0, y: 30 }}
          transition={{ delay: 1.2, duration: 0.6 }}
          className="text-center mt-16"
        >
          <div className="bg-gradient-to-r from-orange-600 to-yellow-500 rounded-2xl p-8 text-white">
            <h3 className="text-2xl md:text-3xl font-bold mb-4 marathi-text">
              तुम्हीही आमच्या समाधान झालेल्या ग्राहकांमध्ये सामील व्हा!
            </h3>
            <p className="text-lg mb-6 text-orange-100">
              Join our satisfied customers today!
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <a
                href="tel:+919876543210"
                className="bg-white text-orange-600 px-8 py-3 rounded-full font-bold hover:bg-orange-50 transition-colors duration-200"
              >
                <span className="marathi-text">आत्ताच कॉल करा</span>
              </a>
              <a
                href="/contact"
                className="border-2 border-white text-white px-8 py-3 rounded-full font-bold hover:bg-white hover:text-orange-600 transition-all duration-200"
              >
                <span className="marathi-text">संपर्क करा</span>
              </a>
            </div>
          </div>
        </motion.div>
      </div>
    </section>
  );
};

export default TestimonialsSection;
