{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 18, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Chava%20Electricals/chava-electricals/src/components/Hero.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/Hero.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/Hero.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAA2R,GACxT,yDACA", "debugId": null}}, {"offset": {"line": 30, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Chava%20Electricals/chava-electricals/src/components/Hero.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/Hero.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/Hero.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAuQ,GACpS,qCACA", "debugId": null}}, {"offset": {"line": 42, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 50, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Chava%20Electricals/chava-electricals/src/components/OffersMarquee.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/OffersMarquee.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/OffersMarquee.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAoS,GACjU,kEACA", "debugId": null}}, {"offset": {"line": 62, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Chava%20Electricals/chava-electricals/src/components/OffersMarquee.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/OffersMarquee.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/OffersMarquee.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAgR,GAC7S,8CACA", "debugId": null}}, {"offset": {"line": 74, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 82, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Chava%20Electricals/chava-electricals/src/components/ServicesPreview.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/ServicesPreview.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ServicesPreview.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAsS,GACnU,oEACA", "debugId": null}}, {"offset": {"line": 94, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Chava%20Electricals/chava-electricals/src/components/ServicesPreview.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/ServicesPreview.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ServicesPreview.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAkR,GAC/S,gDACA", "debugId": null}}, {"offset": {"line": 106, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 114, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Chava%20Electricals/chava-electricals/src/components/WhyChooseUs.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/WhyChooseUs.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/WhyChooseUs.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAkS,GAC/T,gEACA", "debugId": null}}, {"offset": {"line": 126, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Chava%20Electricals/chava-electricals/src/components/WhyChooseUs.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/WhyChooseUs.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/WhyChooseUs.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAA8Q,GAC3S,4CACA", "debugId": null}}, {"offset": {"line": 138, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 146, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Chava%20Electricals/chava-electricals/src/components/TestimonialsSection.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/TestimonialsSection.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/TestimonialsSection.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAA0S,GACvU,wEACA", "debugId": null}}, {"offset": {"line": 158, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Chava%20Electricals/chava-electricals/src/components/TestimonialsSection.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/TestimonialsSection.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/TestimonialsSection.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAsR,GACnT,oDACA", "debugId": null}}, {"offset": {"line": 170, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 178, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Chava%20Electricals/chava-electricals/src/components/ImageGallery.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/ImageGallery.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ImageGallery.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAmS,GAChU,iEACA", "debugId": null}}, {"offset": {"line": 190, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Chava%20Electricals/chava-electricals/src/components/ImageGallery.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/ImageGallery.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ImageGallery.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAA+Q,GAC5S,6CACA", "debugId": null}}, {"offset": {"line": 202, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 210, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Chava%20Electricals/chava-electricals/src/app/page.tsx"], "sourcesContent": ["/*\n * Chava Electricals Website - Homepage\n * Copyright (c) 2025 Eria Software Solutions and Services Pvt Ltd\n * All rights reserved.\n */\n\nimport Hero from '@/components/Hero';\nimport OffersMarquee from '@/components/OffersMarquee';\nimport ServicesPreview from '@/components/ServicesPreview';\nimport WhyChooseUs from '@/components/WhyChooseUs';\nimport TestimonialsSection from '@/components/TestimonialsSection';\nimport ImageGallery from '@/components/ImageGallery';\n\nexport default function Home() {\n  // Sample images for the gallery\n  const galleryImages = [\n    {\n      src: 'https://images.unsplash.com/photo-1665242043190-0ef29390d289?w=800&h=800&fit=crop',\n      alt: 'इलेक्ट्रिकल वायरिंग काम',\n      title: 'घरगुती वायरिंग',\n      category: 'वायरिंग'\n    },\n    {\n      src: 'https://images.unsplash.com/photo-1676630656246-3047520adfdf?w=800&h=800&fit=crop',\n      alt: 'स्विच बोर्ड इन्स्टॉलेशन',\n      title: 'स्विच बोर्ड सेटअप',\n      category: 'स्विच बोर्ड'\n    },\n    {\n      src: 'https://images.unsplash.com/photo-1618090584126-129cd1f3fbae?w=800&h=800&fit=crop',\n      alt: 'इलेक्ट्रिकल रिपेअर काम',\n      title: 'इलेक्ट्रिकल रिपेअर',\n      category: 'रिपेअर'\n    },\n    {\n      src: 'https://images.unsplash.com/photo-1595856619767-ab739fa7daae?w=800&h=800&fit=crop',\n      alt: 'पावर लाइन मेंटेनन्स',\n      title: 'पावर लाइन काम',\n      category: 'मेंटेनन्स'\n    },\n    {\n      src: 'https://images.unsplash.com/photo-1676630444903-163fe485c5d1?w=800&h=800&fit=crop',\n      alt: 'सर्किट बोर्ड रिपेअर',\n      title: 'सर्किट बोर्ड काम',\n      category: 'रिपेअर'\n    },\n    {\n      src: 'https://images.unsplash.com/photo-1467733238130-bb6846885316?w=800&h=800&fit=crop',\n      alt: 'स्विच इन्स्टॉलेशन',\n      title: 'मॉड्यूलर स्विच',\n      category: 'स्विच बोर्ड'\n    }\n  ];\n\n  return (\n    <div className=\"bg-pattern\">\n      <Hero />\n      <OffersMarquee />\n      <ServicesPreview />\n      <WhyChooseUs />\n      <ImageGallery\n        images={galleryImages}\n        title=\"आमच्या कामाचे फोटो\"\n        description=\"छावा इलेक्ट्रिकल्सच्या व्यावसायिक सेवांचे नमुने\"\n      />\n      <TestimonialsSection />\n    </div>\n  );\n}\n"], "names": [], "mappings": "AAAA;;;;CAIC;;;;AAED;AACA;AACA;AACA;AACA;AACA;;;;;;;;AAEe,SAAS;IACtB,gCAAgC;IAChC,MAAM,gBAAgB;QACpB;YACE,KAAK;YACL,KAAK;YACL,OAAO;YACP,UAAU;QACZ;QACA;YACE,KAAK;YACL,KAAK;YACL,OAAO;YACP,UAAU;QACZ;QACA;YACE,KAAK;YACL,KAAK;YACL,OAAO;YACP,UAAU;QACZ;QACA;YACE,KAAK;YACL,KAAK;YACL,OAAO;YACP,UAAU;QACZ;QACA;YACE,KAAK;YACL,KAAK;YACL,OAAO;YACP,UAAU;QACZ;QACA;YACE,KAAK;YACL,KAAK;YACL,OAAO;YACP,UAAU;QACZ;KACD;IAED,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC,0HAAA,CAAA,UAAI;;;;;0BACL,8OAAC,mIAAA,CAAA,UAAa;;;;;0BACd,8OAAC,qIAAA,CAAA,UAAe;;;;;0BAChB,8OAAC,iIAAA,CAAA,UAAW;;;;;0BACZ,8OAAC,kIAAA,CAAA,UAAY;gBACX,QAAQ;gBACR,OAAM;gBACN,aAAY;;;;;;0BAEd,8OAAC,yIAAA,CAAA,UAAmB;;;;;;;;;;;AAG1B", "debugId": null}}]}