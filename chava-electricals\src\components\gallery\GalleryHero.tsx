/*
 * Chava Electricals Website - Gallery Hero Component
 * Copyright (c) 2025 Eria Software Solutions and Services Pvt Ltd
 * All rights reserved.
 */

'use client';

import { motion } from 'framer-motion';
import { Camera, Video, Award, Users } from 'lucide-react';

const GalleryHero = () => {
  const stats = [
    {
      icon: <Camera className="h-6 w-6" />,
      number: "500+",
      label: "प्रकल्प फोटो",
      english: "Project Photos"
    },
    {
      icon: <Video className="h-6 w-6" />,
      number: "50+",
      label: "काम व्हिडिओ",
      english: "Work Videos"
    },
    {
      icon: <Award className="h-6 w-6" />,
      number: "100%",
      label: "समाधान दर",
      english: "Satisfaction Rate"
    },
    {
      icon: <Users className="h-6 w-6" />,
      number: "500+",
      label: "खुश ग्राहक",
      english: "Happy Customers"
    }
  ];

  return (
    <section className="relative bg-gradient-to-br from-yellow-200 via-yellow-300 to-yellow-400 py-20">
      {/* Background Pattern */}
      <div className="absolute inset-0 opacity-10">
        <div className="absolute top-1/4 left-1/4 w-32 h-32 border-2 border-white rounded-full spark-animation"></div>
        <div className="absolute top-3/4 right-1/4 w-24 h-24 border-2 border-white rounded-full spark-animation" style={{animationDelay: '1s'}}></div>
        <div className="absolute top-1/2 left-1/2 w-16 h-16 border-2 border-white rounded-full spark-animation" style={{animationDelay: '2s'}}></div>
      </div>

      <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center text-gray-800">
        <motion.div
          initial={{ opacity: 0, y: 50 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          className="space-y-8"
        >
          {/* Title */}
          <div className="space-y-4">
            <motion.h1
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.2 }}
              className="text-5xl md:text-6xl font-bold marathi-text"
            >
              आमचे काम
            </motion.h1>
            
           
          </div>

          {/* Description */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.6 }}
            className="space-y-4"
          >
            <p className="text-xl md:text-2xl text-gray-600 max-w-4xl mx-auto marathi-text">
              आमच्या पूर्ण केलेल्या प्रकल्पांचे फोटो आणि व्हिडिओ पहा
            </p>
            
          </motion.div>

          {/* Stats */}
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.8 }}
            className="grid grid-cols-2 md:grid-cols-4 gap-6 max-w-4xl mx-auto"
          >
            {stats.map((stat, index) => (
              <motion.div
                key={index}
                initial={{ scale: 0 }}
                animate={{ scale: 1 }}
                transition={{ delay: 1 + index * 0.1, type: "spring", stiffness: 200 }}
                className="bg-white/80 backdrop-blur-sm rounded-xl p-4 text-center shadow-lg"
              >
                <div className="flex justify-center mb-2">
                  {stat.icon}
                </div>
                {stat.number && (
                  <div className="text-2xl font-bold mb-1">
                    {stat.number}
                  </div>
                )}
                <div className="text-sm marathi-text">
                  {stat.label}
                </div>
                <div className="text-xs text-gray-500">
                  {stat.english}
                </div>
              </motion.div>
            ))}
          </motion.div>

          {/* Scroll Indicator */}
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 1.5 }}
            className="pt-8"
          >
            <motion.div
              animate={{ y: [0, 10, 0] }}
              transition={{ duration: 2, repeat: Infinity }}
              className="text-gray-600"
            >
              <p className="text-sm marathi-text mb-2">खाली स्क्रॉल करा</p>
              <div className="w-6 h-10 border-2 border-gray-600 rounded-full mx-auto flex justify-center">
                <motion.div
                  animate={{ y: [0, 12, 0] }}
                  transition={{ duration: 2, repeat: Infinity }}
                  className="w-1 h-3 bg-gray-600 rounded-full mt-2"
                ></motion.div>
              </div>
            </motion.div>
          </motion.div>
        </motion.div>
      </div>
    </section>
  );
};

export default GalleryHero;
