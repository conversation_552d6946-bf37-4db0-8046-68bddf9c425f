'use client';

import React from 'react';
import { motion } from 'framer-motion';
import { useInView } from 'react-intersection-observer';
import { 
  Home, 
  Lightbulb, 
  Fan, 
  Wrench, 
  Zap, 
  Shield, 
  Phone, 
  CheckCircle,
  ArrowRight,
  Clock,
  Star,
  Award
} from 'lucide-react';
import Link from 'next/link';

const ServicesPage = () => {
  const [ref, inView] = useInView({
    triggerOnce: true,
    threshold: 0.1
  });

  const services = [
    {
      icon: <Home className="h-16 w-16" />,
      title: "घरगुती वायरिंग",
      description: "संपूर्ण घरासाठी सुरक्षित आणि आधुनिक वायरिंग सोल्यूशन. आमच्या तज्ञ तंत्रज्ञांकडून व्यावसायिक वायरिंग सेवा घ्या.",
      features: [
        "नवीन कनेक्शन स्थापना",
        "जुने वायरिंग अपग्रेड",
        "सुरक्षा तपासणी",
        "लोड कॅल्क्युलेशन",
        "अर्थिंग सिस्टम"
      ],
      price: "₹5,000 पासून",
      duration: "1-3 दिवस"
    },
    {
      icon: <Lightbulb className="h-16 w-16" />,
      title: "सेन्सर लाइटिंग",
      description: "ऑटोमॅटिक सेन्सर लाइट इन्स्टॉलेशन आणि मेंटेनन्स. ऊर्जा बचत करणारे स्मार्ट लाइटिंग सोल्यूशन.",
      features: [
        "मोशन सेन्सर लाइट्स",
        "टाइमर कंट्रोल",
        "स्मार्ट स्विच",
        "LED अपग्रेड",
        "डिमर कंट्रोल"
      ],
      price: "₹1,500 पासून",
      duration: "2-4 तास"
    },
    {
      icon: <Fan className="h-16 w-16" />,
      title: "फॅन इन्स्टॉलेशन",
      description: "सीलिंग फॅन, एक्झॉस्ट फॅन आणि इतर फॅन्सची व्यावसायिक स्थापना. सर्व प्रकारच्या फॅन्सची दुरुस्ती आणि मेंटेनन्स.",
      features: [
        "सीलिंग फॅन स्थापना",
        "एक्झॉस्ट फॅन",
        "टेबल फॅन रिपेअर",
        "रेग्युलेटर सेटअप",
        "फॅन बॅलन्सिंग"
      ],
      price: "₹800 पासून",
      duration: "1-2 तास"
    },
    {
      icon: <Wrench className="h-16 w-16" />,
      title: "रिपेअर सर्विसेस",
      description: "सर्व प्रकारच्या इलेक्ट्रिकल समस्यांचे त्वरित निराकरण. 24/7 इमर्जन्सी सेवा उपलब्ध.",
      features: [
        "इमर्जन्सी रिपेअर",
        "फॉल्ट डिटेक्शन",
        "प्रिव्हेंटिव्ह मेंटेनन्स",
        "शॉर्ट सर्किट सोल्यूशन",
        "पावर कट समस्या"
      ],
      price: "₹500 पासून",
      duration: "30 मिनिटे - 2 तास"
    },
    {
      icon: <Zap className="h-16 w-16" />,
      title: "स्विच बोर्ड सेटअप",
      description: "आधुनिक स्विच बोर्ड इन्स्टॉलेशन आणि अपग्रेड. मॉड्यूलर स्विच आणि स्मार्ट कंट्रोल सिस्टम.",
      features: [
        "मॉड्यूलर स्विच बोर्ड",
        "डिमर कंट्रोल",
        "USB सॉकेट",
        "स्मार्ट स्विच",
        "टाइमर स्विच"
      ],
      price: "₹2,000 पासून",
      duration: "2-4 तास"
    },
    {
      icon: <Shield className="h-16 w-16" />,
      title: "सेफ्टी चेकअप",
      description: "संपूर्ण इलेक्ट्रिकल सिस्टमची सुरक्षा तपासणी. आग आणि शॉक यापासून संरक्षण.",
      features: [
        "अर्थिंग चेक",
        "लीकेज टेस्ट",
        "लोड बॅलन्सिंग",
        "इन्सुलेशन टेस्ट",
        "सेफ्टी सर्टिफिकेट"
      ],
      price: "₹1,000 पासून",
      duration: "1-2 तास"
    }
  ];

  const whyChooseUs = [
    {
      icon: <Award className="h-8 w-8" />,
      title: "10+ वर्षांचा अनुभव",
      description: "दशकभराचा व्यावसायिक अनुभव"
    },
    {
      icon: <Shield className="h-8 w-8" />,
      title: "लायसन्स प्राप्त",
      description: "सरकारी मान्यताप्राप्त सेवा"
    },
    {
      icon: <Clock className="h-8 w-8" />,
      title: "24/7 उपलब्ध",
      description: "कधीही इमर्जन्सी सेवा"
    },
    {
      icon: <Star className="h-8 w-8" />,
      title: "गुणवत्ता हमी",
      description: "सर्व कामांची गुणवत्ता हमी"
    }
  ];

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.2
      }
    }
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 50 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.6
      }
    }
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Hero Section */}
      <section className="bg-gradient-to-r from-blue-700 via-teal-600 to-green-600 text-white py-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="text-center"
          >
            <h1 className="text-4xl md:text-6xl font-bold mb-6 marathi-text">
              आमच्या सेवा
            </h1>
            <p className="text-xl md:text-2xl mb-8 marathi-text">
              छावा इलेक्ट्रिकल्स - महाराष्ट्रातील विश्वसनीय इलेक्ट्रिकल सेवा
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link
                href="tel:+919876543210"
                className="inline-flex items-center bg-white text-blue-700 px-8 py-4 rounded-full font-bold text-lg hover:bg-gray-100 transition-all duration-300"
              >
                <Phone className="h-6 w-6 mr-2" />
                <span className="marathi-text">आता कॉल करा</span>
              </Link>
              <Link
                href="#services"
                className="inline-flex items-center border-2 border-white text-white px-8 py-4 rounded-full font-bold text-lg hover:bg-white hover:text-blue-700 transition-all duration-300"
              >
                <span className="marathi-text">सेवा पहा</span>
                <ArrowRight className="h-6 w-6 ml-2" />
              </Link>
            </div>
          </motion.div>
        </div>
      </section>

      {/* Services Grid */}
      <section id="services" className="py-20" ref={ref}>
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={inView ? { opacity: 1, y: 0 } : { opacity: 0, y: 30 }}
            transition={{ duration: 0.8 }}
            className="text-center mb-16"
          >
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4 marathi-text">
              आमच्या सेवा
            </h2>
            <p className="text-xl text-gray-600 marathi-text">
              व्यावसायिक आणि विश्वसनीय इलेक्ट्रिकल सोल्यूशन
            </p>
          </motion.div>

          <motion.div
            initial="hidden"
            animate={inView ? "visible" : "hidden"}
            variants={containerVariants}
            className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8"
          >
            {services.map((service, index) => (
              <motion.div
                key={index}
                variants={itemVariants}
                className="bg-white rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-2 p-8 group"
              >
                <div className="text-teal-600 mb-6 group-hover:scale-110 transition-transform duration-300 flex justify-center">
                  {service.icon}
                </div>
                
                <h3 className="text-2xl font-bold text-gray-900 mb-4 marathi-text text-center">
                  {service.title}
                </h3>
                
                <p className="text-gray-600 mb-6 marathi-text text-center">
                  {service.description}
                </p>

                <div className="mb-6">
                  <h4 className="text-lg font-semibold text-gray-900 mb-3 marathi-text">
                    सेवेत समाविष्ट:
                  </h4>
                  <ul className="space-y-2">
                    {service.features.map((feature, idx) => (
                      <li key={idx} className="flex items-center text-sm text-gray-600">
                        <CheckCircle className="h-4 w-4 text-teal-500 mr-2 flex-shrink-0" />
                        <span className="marathi-text">{feature}</span>
                      </li>
                    ))}
                  </ul>
                </div>

                <div className="border-t pt-6">
                  <div className="flex justify-between items-center mb-4">
                    <div>
                      <p className="text-sm text-gray-500 marathi-text">किंमत</p>
                      <p className="text-lg font-bold text-teal-600">{service.price}</p>
                    </div>
                    <div>
                      <p className="text-sm text-gray-500 marathi-text">वेळ</p>
                      <p className="text-lg font-bold text-gray-900 marathi-text">{service.duration}</p>
                    </div>
                  </div>
                  
                  <Link
                    href="tel:+919876543210"
                    className="w-full bg-teal-600 text-white py-3 rounded-lg font-semibold hover:bg-teal-700 transition-colors duration-200 flex items-center justify-center"
                  >
                    <Phone className="h-5 w-5 mr-2" />
                    <span className="marathi-text">बुकिंग करा</span>
                  </Link>
                </div>
              </motion.div>
            ))}
          </motion.div>
        </div>
      </section>

      {/* Why Choose Us Section */}
      <section className="py-20 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={inView ? { opacity: 1, y: 0 } : { opacity: 0, y: 30 }}
            transition={{ duration: 0.8, delay: 0.2 }}
            className="text-center mb-16"
          >
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4 marathi-text">
              आम्हाला का निवडावे?
            </h2>
            <p className="text-xl text-gray-600 marathi-text">
              आमच्या विशेषता आणि फायदे
            </p>
          </motion.div>

          <motion.div
            initial="hidden"
            animate={inView ? "visible" : "hidden"}
            variants={containerVariants}
            className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8"
          >
            {whyChooseUs.map((item, index) => (
              <motion.div
                key={index}
                variants={itemVariants}
                className="text-center p-6 rounded-xl bg-gray-50 hover:bg-teal-50 transition-all duration-300 transform hover:-translate-y-2 group"
              >
                <div className="text-teal-600 mb-4 flex justify-center group-hover:scale-110 transition-transform duration-300">
                  {item.icon}
                </div>
                <h4 className="text-lg font-bold text-gray-900 mb-3 marathi-text">
                  {item.title}
                </h4>
                <p className="text-gray-600 marathi-text">
                  {item.description}
                </p>
              </motion.div>
            ))}
          </motion.div>
        </div>
      </section>

      {/* Contact CTA Section */}
      <section className="py-20 bg-gradient-to-r from-blue-700 to-teal-600">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={inView ? { opacity: 1, y: 0 } : { opacity: 0, y: 30 }}
            transition={{ duration: 0.8, delay: 0.4 }}
          >
            <h2 className="text-3xl md:text-4xl font-bold text-white mb-6 marathi-text">
              आजच आमच्याशी संपर्क साधा
            </h2>
            <p className="text-xl text-blue-100 mb-8 marathi-text">
              24/7 इमर्जन्सी सेवा उपलब्ध • मोफत कोटेशन • त्वरित सेवा
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link
                href="tel:+919876543210"
                className="inline-flex items-center bg-white text-blue-700 px-8 py-4 rounded-full font-bold text-lg hover:bg-gray-100 transition-all duration-300 transform hover:scale-105"
              >
                <Phone className="h-6 w-6 mr-2" />
                <span className="marathi-text">+91 98765 43210</span>
              </Link>
              <Link
                href="/contact"
                className="inline-flex items-center border-2 border-white text-white px-8 py-4 rounded-full font-bold text-lg hover:bg-white hover:text-blue-700 transition-all duration-300 transform hover:scale-105"
              >
                <span className="marathi-text">संपर्क फॉर्म</span>
                <ArrowRight className="h-6 w-6 ml-2" />
              </Link>
            </div>
          </motion.div>
        </div>
      </section>

      {/* Emergency Banner */}
      <section className="bg-red-600 text-white py-4">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-center text-center">
            <Clock className="h-6 w-6 mr-2 animate-pulse" />
            <p className="text-lg font-semibold marathi-text">
              इमर्जन्सी सेवा: 24/7 उपलब्ध • कॉल करा: +91 98765 43210
            </p>
          </div>
        </div>
      </section>
    </div>
  );
};

export default ServicesPage;
