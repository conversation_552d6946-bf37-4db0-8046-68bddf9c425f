@import url('https://fonts.googleapis.com/css2?family=Noto+Sans+Devanagari:wght@300;400;500;600;700&display=swap');
@import "tailwindcss";

/*
 * Chava Electricals Website - Global Styles
 * Copyright (c) 2025 Eria Software Solutions and Services Pvt Ltd
 * All rights reserved.
 */

:root {
  --background: #ffffff;
  --foreground: #171717;
  --primary-blue: #1e40af;
  --primary-teal: #0d9488;
  --accent-green: #059669;
  --secondary-gray: #6b7280;
}

@media (prefers-color-scheme: dark) {
  :root {
    --background: #0a0a0a;
    --foreground: #ededed;
  }
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: 'Inter', 'Noto Sans Devanagari', sans-serif;
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
  background: var(--primary-blue);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #1e3a8a;
}

/* Marathi text styling */
.marathi-text {
  font-family: 'Noto Sans Devanagari', sans-serif;
  font-weight: 500;
}

/* Electrical theme animations */
@keyframes spark {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.3; }
}

.spark-animation {
  animation: spark 2s ease-in-out infinite;
}

@keyframes glow {
  0%, 100% { box-shadow: 0 0 5px var(--primary-blue); }
  50% { box-shadow: 0 0 20px var(--primary-blue), 0 0 30px var(--primary-blue); }
}

.glow-animation {
  animation: glow 3s ease-in-out infinite;
}

/* Gradient backgrounds */
.professional-gradient {
  background: linear-gradient(135deg, var(--primary-blue) 0%, var(--primary-teal) 50%, var(--accent-green) 100%);
}

.electrical-gradient {
  background: linear-gradient(135deg, var(--primary-blue) 0%, var(--primary-teal) 100%);
}

/* Custom utilities */
.bg-pattern {
  background-image:
    radial-gradient(circle at 25% 25%, rgba(30, 64, 175, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 75% 75%, rgba(13, 148, 136, 0.1) 0%, transparent 50%);
}
